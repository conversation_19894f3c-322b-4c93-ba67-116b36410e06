<template>
  <div class="precipitation-drawing">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧工具栏 -->
      <div class="left-toolbar">
        <!-- 工具栏标题 -->
        <div class="toolbar-title">
          <i class="el-icon-brush" />
          <span>绘制工具</span>
        </div>

        <!-- 降水量级选择 -->
        <div class="precipitation-levels">
          <div class="level-title">降水量级</div>
          <div class="level-buttons">
            <div
              v-for="level in precipitationLevels"
              :key="level.value"
              :class="{ 'active': selectedLevel === level.value }"
              :title="level.label + '（' + level.range + '）'"
              class="level-button"
              @click="selectLevel(level.value)"
            >
              <div :style="{ backgroundColor: level.color }" class="color-indicator" />
              <div class="level-content">
                <span class="level-name">{{ level.shortLabel }}</span>
                <span class="level-range">{{ level.range }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 绘制控制按钮 -->
        <div class="drawing-controls">
          <el-button
            :disabled="!selectedLevel"
            :class="{ 'drawing-active': isDrawing }"
            type="primary"
            size="small"
            class="control-button"
            @click="startDrawing"
          >
            <i class="el-icon-edit" />
            <span>{{ isDrawing ? '绘制中' : '开始绘制' }}</span>
          </el-button>

          <el-button
            :disabled="!selectedLevel || !hasDrawnAreas"
            type="warning"
            size="small"
            class="control-button"
            @click="clearCurrentLevel"
          >
            <i class="el-icon-delete" />
            <span>清除当前</span>
          </el-button>

          <el-button
            :disabled="!hasAnyDrawnAreas"
            type="danger"
            size="small"
            class="control-button"
            @click="clearAllAreas"
          >
            <i class="el-icon-refresh-left" />
            <span>清除全部</span>
          </el-button>

          <!-- 拖拽上传区域 -->
          <el-upload
            ref="fileUpload"
            :action="uploadAction"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="onUploadSuccess"
            :on-error="onUploadError"
            :show-file-list="false"
            :auto-upload="true"
            accept="*"
            drag
            :class="['drag-upload-component', { 'uploading': uploading }]"
          >
            <div class="drag-upload-content">
              <div class="upload-icon-wrapper">
                <i :class="uploading ? 'el-icon-loading' : 'el-icon-upload2'" class="drag-upload-icon" />
              </div>
              <div class="drag-upload-text">
                <span v-if="uploading" class="uploading-text">
                  正在上传...
                </span>
                <template v-else>
                  <div class="drag-main-text">拖拽文件到此处</div>
                  <div class="drag-sub-text">支持点击</div>
                </template>
              </div>
            </div>
          </el-upload>
        </div>

        <!-- 站点显示控制 -->
        <div class="station-control-section">
          <div class="station-control-title">
            <i class="el-icon-location" />
            <span>站点显示</span>
          </div>
          <div class="station-control-options">
            <div class="station-checkbox-group">
              <el-checkbox
                v-model="selectedStationTypes"
                label="national"
                class="station-checkbox"
              >
                <span class="checkbox-text">国家站</span>
              </el-checkbox>
              <el-checkbox
                v-model="selectedStationTypes"
                label="regional"
                class="station-checkbox"
              >
                <span class="checkbox-text">区域站</span>
              </el-checkbox>
            </div>
          </div>

        </div>

      </div>

      <!-- 右侧地图区域 -->
      <div class="map-area">
        <!-- 地图容器 -->
        <div class="map-container">
          <div id="precipitation-map" class="map-canvas">
            <!-- 地图加载提示 -->
            <div v-if="!mapLoaded" class="map-loading">
              <i class="el-icon-loading" />
              <span>地图加载中...</span>
            </div>

            <!-- 地图图例 -->
            <div v-if="mapLoaded" class="map-legend">
              <div class="legend-title">降水量级图例</div>
              <div class="legend-items">
                <div
                  v-for="level in precipitationLevels"
                  :key="level.value"
                  class="legend-item"
                >
                  <div :style="{ backgroundColor: level.color }" class="legend-color" />
                  <span class="legend-label">{{ level.label }}（{{ level.range }}）</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 绘制统计 -->
    <div v-if="hasAnyDrawnAreas" class="drawing-stats">
      <div class="stats-title">绘制统计</div>
      <div class="stats-content">
        <div v-for="level in drawnLevels" :key="level.value" class="stat-item">
          <span class="stat-label">{{ level.label }}：</span>
          <span class="stat-value">{{ getAreaCount(level.value) }} 个落区</span>
        </div>
        <div class="stat-total">
          <span class="stat-label">总计：</span>
          <span class="stat-value">{{ totalAreaCount }} 个落区</span>
        </div>
      </div>
    </div>

    <!-- 站点信息弹窗 -->
    <div
      v-if="showStationPopup && stationPopupInfo"
      :style="{
        left: stationPopupPosition.x + 'px',
        top: stationPopupPosition.y + 'px'
      }"
      class="station-popup"
    >
      <div class="popup-header">
        <span class="popup-title">站点信息</span>
        <button class="popup-close" @click="closeStationPopup">×</button>
      </div>
      <div class="popup-content">
        <div class="info-row">
          <span class="info-label">站名：</span>
          <span class="info-value">{{ stationPopupInfo.name || '-' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">站号：</span>
          <span class="info-value">{{ stationPopupInfo.id || '-' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">所属市：</span>
          <span class="info-value">{{ stationPopupInfo.city || '-' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">所属县：</span>
          <span class="info-value">{{ stationPopupInfo.cnty || '-' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">站点级别：</span>
          <span class="info-value">{{ getLevelText(stationPopupInfo.level) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import 'ol/ol.css'
import { Map, View } from 'ol'
import LayerVector from 'ol/layer/Vector'
import TileLayer from 'ol/layer/Tile'
import XYZ from 'ol/source/XYZ'
import { fromLonLat } from 'ol/proj'
import SourceVector from 'ol/source/Vector'
import GeoJSON from 'ol/format/GeoJSON'
import { Style, Stroke, Fill, Circle as CircleStyle, Text } from 'ol/style'
import Draw from 'ol/interaction/Draw'
import { Feature } from 'ol'
import { Polygon, Point } from 'ol/geom'
import Modify from 'ol/interaction/Modify'
import Snap from 'ol/interaction/Snap'
import { unByKey } from 'ol/Observable'
import { defaults as defaultInteractions } from 'ol/interaction'
import { getToken } from '@/utils/auth'

// 使用本地底图服务，移除天地图token

export default {
  name: 'PrecipitationDrawing',
  props: {
    // 初始绘制数据
    initialData: {
      type: Object,
      default: () => ({})
    },
    // 预报区域
    region: {
      type: String,
      default: 'region1'
    },
    // 是否只读模式
    readonly: {
      type: Boolean,
      default: false
    },
    // 站点数据
    stationData: {
      type: Array,
      default: () => []
    },
    // 是否显示站点
    showStations: {
      type: Boolean,
      default: true
    },
    // 站点数据加载状态
    stationLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      map: null,
      mapLoaded: false,
      baseLayer: null,
      annotationLayer: null,
      precipitationLayer: null,
      precipitationSource: null,

      // 站点图层相关
      stationLayer: null,
      stationSource: null,

      // 站点信息弹窗相关
      showStationPopup: false,
      stationPopupInfo: null,
      stationPopupPosition: { x: 0, y: 0 },

      // 站点显示控制
      selectedStationTypes: ['national'], // 默认显示所有类型
      originalStationData: [], // 保存原始站点数据

      // 绘制相关
      isDrawing: false,
      selectedLevel: '',
      drawInteraction: null,
      modifyInteraction: null,
      snapInteraction: null,
      drawListener: null,
      doubleClickListener: null,
      lastClickTime: 0,
      clickTimeout: null,

      // 文件上传相关
      uploading: false,
      uploadAction: process.env.VUE_APP_BASE_API + '/exam/api/weather/case/upload/precipitation-area',

      // 降水量级配置（取消无雨显示，level0为小雨，level10为中雨，依次类推）
      precipitationLevels: [
        { label: '小雨', range: '0~9.9mm', shortLabel: '小雨', value: 'level0', color: '#A6F28E' },
        { label: '中雨', range: '10~24.9mm', shortLabel: '中雨', value: 'level10', color: '#3DB93D' },
        { label: '大雨', range: '25~49.9mm', shortLabel: '大雨', value: 'level25', color: '#61B8FF' },
        { label: '暴雨', range: '50~99.9mm', shortLabel: '暴雨', value: 'level50', color: '#0000FE' },
        { label: '大暴雨', range: '100~249.9mm', shortLabel: '大暴', value: 'level100', color: '#FA01F3' },
        { label: '特大暴雨', range: '250mm以上', shortLabel: '特大', value: 'level250', color: '#8B0000' }
      ],

      // 绘制数据存储
      drawnAreas: {}, // 按量级存储绘制的区域

      // 区域中心点配置
      regionCenters: {
        'region1': [116.4074, 39.9042], // 华北区域 - 北京
        'region2': [125.3245, 43.8868], // 东北区域 - 长春
        'region3': [118.7969, 32.0603], // 长江中下游区域 - 南京
        'region4': [113.2644, 23.1291], // 华南区域 - 广州
        'region5': [104.0665, 30.5728], // 西南地区东部 - 成都
        'region6': [91.1322, 29.6544], // 青藏高原区域 - 拉萨
        'region7': [87.6168, 43.7928], // 新疆区域 - 乌鲁木齐
        'region8': [108.9480, 34.2588], // 西北地区东部区域 - 西安
        'region9': [111.7519, 40.8425] // 内蒙古区域 - 呼和浩特
      }
    }
  },
  computed: {
    uploadHeaders() {
      return {
        'token': getToken()
      }
    },
    hasDrawnAreas() {
      return this.selectedLevel && this.drawnAreas[this.selectedLevel] && this.drawnAreas[this.selectedLevel].length > 0
    },
    hasAnyDrawnAreas() {
      return Object.values(this.drawnAreas).some(areas => areas && areas.length > 0)
    },
    drawnLevels() {
      return this.precipitationLevels.filter(level =>
        this.drawnAreas[level.value] && this.drawnAreas[level.value].length > 0
      )
    },
    totalAreaCount() {
      return Object.values(this.drawnAreas).reduce((total, areas) => {
        return total + (areas ? areas.length : 0)
      }, 0)
    }
  },
  watch: {
    // 监听区域变化，重新定位地图中心
    region(newRegion) {
      if (this.map && newRegion) {
        this.updateMapCenter(newRegion)
      }
    },

    // 监听站点数据变化
    stationData: {
      handler(newStationData) {
        console.log('站点数据发生变化:', newStationData ? newStationData.length : 0, '个站点')
        if (this.mapLoaded && this.showStations) {
          this.$nextTick(() => {
            this.updateStationDisplay(newStationData)
          })
        }
      },
      deep: true,
      immediate: true
    },

    // 监听初始数据变化
    initialData: {
      handler(newData) {
        if (newData && Object.keys(newData).length > 0) {
          this.$nextTick(() => {
            this.loadInitialData()
          })
        }
      },
      deep: true,
      immediate: false
    },

    // 监听站点显示类型变化
    selectedStationTypes: {
      handler(newTypes) {
        console.log('站点显示类型变化:', newTypes)
        if (this.mapLoaded && this.originalStationData.length > 0) {
          this.$nextTick(() => {
            this.updateStationDisplay(this.originalStationData)
          })
        }
      },
      deep: true, // 深度监听数组变化
      immediate: false
    }
  },
  mounted() {
    // 确保DOM元素已经渲染完成后再初始化地图
    this.$nextTick(() => {
      this.initMap()
      this.loadInitialData()

      // 添加窗口大小变化监听
      window.addEventListener('resize', this.handleResize)
    })
  },

  beforeDestroy() {
    // 清理事件监听
    window.removeEventListener('resize', this.handleResize)

    // 清理站点点击监听器
    this.removeStationClickListener()

    // 清理地图
    if (this.map) {
      this.map.setTarget(null)
    }
  },
  beforeDestroy() {
    if (this.map) {
      this.map.setTarget(null)
    }
  },
  methods: {
    // 初始化地图
    initMap() {
      try {
        // 检查DOM元素是否存在
        const mapElement = document.getElementById('precipitation-map')
        if (!mapElement) {
          console.error('地图容器元素未找到')
          return
        }

        console.log('开始初始化地图...')

        // 获取区域中心点
        const center = this.regionCenters[this.region] || this.regionCenters['region1']
        console.log('地图中心点:', center)

        // 创建本地底图图层
        this.baseLayer = new TileLayer({
          source: new XYZ({
            url: `http://***********:81/DataServer?T=vec_w&x={x}&y={y}&l={z}`,
            crossOrigin: 'anonymous'
          })
        })

        // 创建本地注记图层
        this.annotationLayer = new TileLayer({
          source: new XYZ({
            url: `http://***********:81/DataServer?T=cva_w&x={x}&y={y}&l={z}`,
            crossOrigin: 'anonymous'
          }),
          opacity: 0.8
        })

        // 创建降水图层
        this.precipitationSource = new SourceVector()
        this.precipitationLayer = new LayerVector({
          source: this.precipitationSource,
          style: this.getFeatureStyle.bind(this),
          zIndex: 5 // 确保在底图之上
        })

        // 初始化站点图层
        this.initStationLayer()

        // 创建地图
        this.map = new Map({
          target: 'precipitation-map',
          layers: [this.baseLayer, this.precipitationLayer, this.stationLayer, this.annotationLayer],
          view: new View({
            center: fromLonLat(center),
            zoom: 7,
            minZoom: 5,
            maxZoom: 15
          }),
          // 禁用双击放大，避免与绘制双击结束冲突
          interactions: defaultInteractions({
            doubleClickZoom: false
          })
        })

        // 监听地图加载完成事件
        this.map.once('rendercomplete', () => {
          console.log('地图渲染完成')
          this.mapLoaded = true

          // 发出地图加载完成事件
          this.$emit('map-loaded')

          // 添加站点点击事件监听
          this.addStationClickListener()

          // 如果已经有站点数据，立即显示
          if (this.stationData && this.stationData.length > 0) {
            console.log('地图加载完成时发现已有站点数据，立即显示:', this.stationData.length, '个站点')
            this.$nextTick(() => {
              this.updateStationDisplay(this.stationData)
            })
          }
        })

        // 强制更新地图尺寸
        setTimeout(() => {
          this.mapLoaded = true
          if (this.map) {
            this.map.updateSize()
            this.map.renderSync()
            console.log('强制更新地图尺寸')
          }
        }, 1000)

        // 再次尝试更新地图尺寸
        setTimeout(() => {
          if (this.map) {
            this.map.updateSize()
            console.log('再次更新地图尺寸')
          }
        }, 3000)

        console.log('地图初始化完成')
      } catch (error) {
        console.error('地图初始化失败:', error)
        this.$message.error('地图初始化失败，请刷新页面重试')
      }
    },

    // 初始化站点图层
    initStationLayer() {
      console.log('初始化站点图层...')

      // 创建站点数据源
      this.stationSource = new SourceVector()

      // 创建站点图层，参考baseMap.vue的实现
      this.stationLayer = new LayerVector({
        source: this.stationSource,
        style: this.getStationStyle.bind(this),
        zIndex: 10 // 确保站点显示在降水图层之上
      })

      console.log('站点图层初始化完成')
    },

    // 获取要素样式
    getFeatureStyle(feature) {
      const level = feature.get('precipitationLevel')
      const levelConfig = this.precipitationLevels.find(l => l.value === level)

      if (!levelConfig) {
        return new Style({
          stroke: new Stroke({
            color: '#666666',
            width: 2
          }),
          fill: new Fill({
            color: 'rgba(102, 102, 102, 0.3)'
          })
        })
      }

      return new Style({
        stroke: new Stroke({
          color: levelConfig.color,
          width: 2
        }),
        fill: new Fill({
          color: levelConfig.color + '4D' // 30% 透明度
        })
      })
    },

    // 获取站点样式
    getStationStyle(feature) {
      var level = feature.get('level') || feature.get('Station_levl') || 3
      level = parseInt(level)

      // 根据站点级别设置不同的样式，参考baseMap.vue
      let radius, fillColor
      if (level > 13) {
        radius = 4
        fillColor = '#27ae60' // 区域站，绿色
      } else if (level <= 13) {
        radius = 5
        fillColor = '#f39c12' // 国家站
      }

      return new Style({
        image: new CircleStyle({
          radius: radius,
          fill: new Fill({
            color: fillColor
          }),
          stroke: new Stroke({
            color: '#ffffff',
            width: 1
          })
        })
      })
    },

    // 选择降水量级
    selectLevel(level) {
      if (this.readonly) return

      this.selectedLevel = level
      this.stopDrawing()
    },

    // 开始绘制
    startDrawing() {
      if (!this.selectedLevel || this.readonly) return

      if (this.isDrawing) {
        this.stopDrawing()
        return
      }

      this.isDrawing = true

      // 创建绘制交互，参考picture.vue的实现
      this.drawInteraction = new Draw({
        source: this.precipitationSource,
        type: 'Polygon',

        // 使用平滑曲线绘制
        geometryFunction: (coordinates, geometry) => {
          if (!geometry) {
            geometry = new Polygon([coordinates[0]])
          } else {
            const coords = coordinates[0]
            // 如果有足够的点，创建平滑曲线
            if (coords.length > 2) {
              try {
                // 创建平滑曲线点
                const smoothCoords = this.createSmoothCurve(coords)
                if (smoothCoords && smoothCoords.length > 2) {
                  // 确保首尾相连
                  if (smoothCoords[0][0] !== smoothCoords[smoothCoords.length - 1][0] ||
                      smoothCoords[0][1] !== smoothCoords[smoothCoords.length - 1][1]) {
                    smoothCoords.push(smoothCoords[0])
                  }
                  geometry.setCoordinates([smoothCoords])
                } else {
                  geometry.setCoordinates([coords])
                }
              } catch (error) {
                console.error('Error creating smooth curve:', error)
                geometry.setCoordinates([coords])
              }
            } else {
              geometry.setCoordinates([coords])
            }
          }
          return geometry
        },

        // 设置绘制样式
        style: this.getDrawingStyle.bind(this),

        // 设置最大点数限制，防止性能问题
        maxPoints: 100,
        // 设置点击容差
        clickTolerance: 6,
        // 禁用默认的双击结束绘制
        finishCondition: () => false
      })

      // 添加绘制完成监听
      this.drawListener = this.drawInteraction.on('drawend', (event) => {
        const feature = event.feature
        const geometry = feature.getGeometry()
        const coordinates = geometry.getCoordinates()[0]

        // 确保首尾相连
        if (coordinates[0][0] !== coordinates[coordinates.length - 1][0] ||
            coordinates[0][1] !== coordinates[coordinates.length - 1][1]) {
          coordinates.push(coordinates[0])
          geometry.setCoordinates([coordinates])
        }

        // 设置特性属性
        feature.set('precipitationLevel', this.selectedLevel)
        feature.set('id', this.generateFeatureId())

        // 存储到数据结构中
        this.addAreaToData(this.selectedLevel, feature)

        // 停止绘制
        this.stopDrawing()

        // 添加修改交互
        this.addModifyInteraction()

        // 触发数据变更事件
        this.emitDataChange()

        this.$message.success('降水落区绘制完成')
      })

      this.map.addInteraction(this.drawInteraction)

      // 添加自定义双击结束绘制监听
      this.addCustomDoubleClickHandler()
    },

    // 停止绘制
    stopDrawing() {
      this.isDrawing = false

      if (this.drawInteraction) {
        this.map.removeInteraction(this.drawInteraction)
        this.drawInteraction = null
      }

      if (this.drawListener) {
        unByKey(this.drawListener)
        this.drawListener = null
      }

      // 清理自定义双击监听器
      this.removeCustomDoubleClickHandler()
    },

    // 生成要素ID
    generateFeatureId() {
      return 'precipitation_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    },

    // 创建平滑曲线
    createSmoothCurve(points) {
      // 如果点数太少，直接返回原始点
      if (points.length < 3) {
        return points
      }

      const smoothPoints = []
      const numOfSegments = 8 // 减少分段数以提高性能

      // 对每一段进行插值
      for (let i = 0; i < points.length; i++) {
        const p0 = points[i === 0 ? points.length - 1 : i - 1]
        const p1 = points[i]
        const p2 = points[(i + 1) % points.length]
        const p3 = points[(i + 2) % points.length]

        // 添加当前点
        smoothPoints.push(p1)

        // 在当前点和下一点之间插值
        for (let t = 1; t <= numOfSegments; t++) {
          const u = t / numOfSegments
          const point = this.catmullRomSpline(p0, p1, p2, p3, u)

          // 验证坐标有效性
          if (this.isValidCoordinate(point)) {
            smoothPoints.push(point)
          }
        }
      }

      return smoothPoints
    },

    // Catmull-Rom样条插值
    catmullRomSpline(p0, p1, p2, p3, t) {
      const t2 = t * t
      const t3 = t2 * t

      const x = 0.5 * ((2 * p1[0]) +
        (-p0[0] + p2[0]) * t +
        (2 * p0[0] - 5 * p1[0] + 4 * p2[0] - p3[0]) * t2 +
        (-p0[0] + 3 * p1[0] - 3 * p2[0] + p3[0]) * t3)

      const y = 0.5 * ((2 * p1[1]) +
        (-p0[1] + p2[1]) * t +
        (2 * p0[1] - 5 * p1[1] + 4 * p2[1] - p3[1]) * t2 +
        (-p0[1] + 3 * p1[1] - 3 * p2[1] + p3[1]) * t3)

      return [x, y]
    },

    // 验证坐标有效性
    isValidCoordinate(coord) {
      return coord &&
             typeof coord[0] === 'number' &&
             typeof coord[1] === 'number' &&
             !isNaN(coord[0]) &&
             !isNaN(coord[1]) &&
             Math.abs(coord[0]) < 20037508.34 &&
             Math.abs(coord[1]) < 20037508.34
    },

    // 获取绘制样式
    getDrawingStyle() {
      const levelConfig = this.precipitationLevels.find(l => l.value === this.selectedLevel)

      return new Style({
        stroke: new Stroke({
          color: levelConfig ? levelConfig.color : '#666666',
          width: 2,
          lineDash: [5, 5]
        }),
        fill: new Fill({
          color: (levelConfig ? levelConfig.color : '#666666') + '33' // 20% 透明度
        })
      })
    },

    // 添加修改交互
    addModifyInteraction() {
      if (this.modifyInteraction) {
        this.map.removeInteraction(this.modifyInteraction)
      }

      this.modifyInteraction = new Modify({
        source: this.precipitationSource
      })

      this.map.addInteraction(this.modifyInteraction)

      // 添加捕捉交互
      if (this.snapInteraction) {
        this.map.removeInteraction(this.snapInteraction)
      }

      this.snapInteraction = new Snap({
        source: this.precipitationSource
      })

      this.map.addInteraction(this.snapInteraction)
    },

    // 添加区域到数据结构
    addAreaToData(level, feature) {
      if (!this.drawnAreas[level]) {
        this.$set(this.drawnAreas, level, [])
      }

      // 将几何数据转换为GeoJSON格式存储
      const geoJsonFormat = new GeoJSON()
      const geoJsonFeature = geoJsonFormat.writeFeature(feature, {
        dataProjection: 'EPSG:4326',
        featureProjection: 'EPSG:3857'
      })

      this.drawnAreas[level].push({
        id: feature.get('id'),
        geometry: JSON.parse(geoJsonFeature).geometry,
        properties: {
          precipitationLevel: level,
          createTime: new Date().toISOString()
        }
      })
    },

    // 清除当前量级的所有落区
    clearCurrentLevel() {
      if (!this.selectedLevel) return

      this.$confirm('确定要清除当前量级的所有落区吗？', '确认清除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从地图上移除要素
        const features = this.precipitationSource.getFeatures()
        const toRemove = features.filter(f => f.get('precipitationLevel') === this.selectedLevel)
        toRemove.forEach(f => this.precipitationSource.removeFeature(f))

        // 从数据结构中移除
        this.$set(this.drawnAreas, this.selectedLevel, [])

        // 触发数据变更事件
        this.emitDataChange()

        this.$message.success('已清除当前量级的所有落区')
      }).catch(() => {
        // 用户取消
      })
    },

    // 清除所有落区
    clearAllAreas() {
      this.$confirm('确定要清除所有降水落区吗？', '确认清除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除地图上的所有要素
        this.precipitationSource.clear()

        // 清除数据结构
        this.drawnAreas = {}

        // 触发数据变更事件
        this.emitDataChange()

        this.$message.success('已清除所有降水落区')
      }).catch(() => {
        // 用户取消
      })
    },

    // 获取指定量级的落区数量
    getAreaCount(level) {
      return this.drawnAreas[level] ? this.drawnAreas[level].length : 0
    },

    // 加载初始数据
    loadInitialData() {
      if (!this.initialData || Object.keys(this.initialData).length === 0) {
        console.log('initialData 为空，退出加载')
        return
      }

      // 确保地图和数据源已经初始化
      if (!this.map || !this.precipitationSource) {
        console.warn('地图或数据源未初始化，延迟加载数据')
        setTimeout(() => {
          this.loadInitialData()
        }, 500)
        return
      }

      try {
        // 清除现有数据
        this.precipitationSource.clear()
        this.drawnAreas = {}

        const geoJsonFormat = new GeoJSON()

        // 处理数据格式：如果有 areas 属性，使用 areas，否则直接使用 initialData
        const dataToLoad = this.initialData.areas || this.initialData
        // 遍历初始数据中的每个量级
        Object.keys(dataToLoad).forEach(level => {
          const areas = dataToLoad[level]

          if (Array.isArray(areas)) {
            this.$set(this.drawnAreas, level, [])

            areas.forEach((areaData) => {
              try {
                let parsedData = areaData

                // 如果是字符串格式，尝试解析
                if (typeof areaData === 'string') {
                  parsedData = this.parseAreaDataString(areaData)
                }

                // 创建要素
                const feature = geoJsonFormat.readFeature({
                  type: 'Feature',
                  geometry: parsedData.geometry,
                  properties: parsedData.properties || {}
                }, {
                  dataProjection: 'EPSG:4326',
                  featureProjection: 'EPSG:3857'
                })

                // 设置要素属性
                feature.set('precipitationLevel', level)
                feature.set('id', parsedData.id || this.generateFeatureId())

                // 添加到地图
                this.precipitationSource.addFeature(feature)

                // 添加到数据结构
                this.drawnAreas[level].push(parsedData)
              } catch (parseError) {
                console.error('解析区域数据失败:', parseError, areaData)
              }
            })
          }
        })

        // 添加修改交互
        this.addModifyInteraction()

        // 注意：加载初始数据时不触发数据变更事件，避免不必要的保存请求
        // this.emitDataChange()
      } catch (error) {
        console.error('加载降水落区数据失败:', error)
        this.$message.error('加载绘制数据失败')
      }
    },

    // 解析字符串格式的区域数据
    parseAreaDataString(dataString) {
      try {
        // 从字符串中提取几何数据和属性
        // 字符串格式类似："{geometry={coordinates=[[[...]], type=Polygon}, id=..., properties={...}}"

        // 提取 coordinates
        const coordsMatch = dataString.match(/coordinates=\[\[\[(.*?)\]\]\]/)
        if (!coordsMatch) {
          throw new Error('无法解析坐标数据')
        }

        // 解析坐标字符串
        const coordsStr = coordsMatch[1]
        const coordinates = []
        const coordPairs = coordsStr.split('], [')

        coordPairs.forEach(pair => {
          const cleanPair = pair.replace(/[\[\]]/g, '')
          const [lng, lat] = cleanPair.split(', ').map(Number)
          if (!isNaN(lng) && !isNaN(lat)) {
            coordinates.push([lng, lat])
          }
        })

        // 提取 ID
        const idMatch = dataString.match(/id=([^,}]+)/)
        const id = idMatch ? idMatch[1] : this.generateFeatureId()

        // 提取 precipitationLevel
        const levelMatch = dataString.match(/precipitationLevel=([^,}]+)/)
        const precipitationLevel = levelMatch ? levelMatch[1] : 'unknown'

        // 提取 createTime
        const timeMatch = dataString.match(/createTime=([^,}]+)/)
        const createTime = timeMatch ? timeMatch[1] : new Date().toISOString()

        return {
          geometry: {
            type: 'Polygon',
            coordinates: [coordinates]
          },
          id: id,
          properties: {
            precipitationLevel: precipitationLevel,
            createTime: createTime
          }
        }
      } catch (error) {
        console.error('解析区域数据字符串失败:', error, dataString)
        throw error
      }
    },

    // 触发数据变更事件
    emitDataChange() {
      const exportData = this.exportDrawingData()
      this.$emit('data-change', exportData)
    },

    // 导出绘制数据
    exportDrawingData() {
      const exportData = {}

      Object.keys(this.drawnAreas).forEach(level => {
        if (this.drawnAreas[level] && this.drawnAreas[level].length > 0) {
          exportData[level] = [...this.drawnAreas[level]]
        }
      })

      return {
        region: this.region,
        areas: exportData,
        totalCount: this.totalAreaCount,
        createTime: new Date().toISOString(),
        version: '1.0'
      }
    },

    // 获取绘制进度
    getProgress() {
      // 简单的进度计算：有绘制内容就是100%，否则是0%
      return this.hasAnyDrawnAreas ? 100 : 0
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.map) {
        setTimeout(() => {
          this.map.updateSize()
        }, 100)
      }
    },

    // 更新地图中心点
    updateMapCenter(region) {
      if (!this.map || !region) return

      const center = this.regionCenters[region] || this.regionCenters['region1']
      const view = this.map.getView()

      // 平滑移动到新的中心点
      view.animate({
        center: fromLonLat(center),
        zoom: 7,
        duration: 1000 // 1秒动画
      })

      console.log('地图中心已更新到:', region, center)
    },

    // 更新站点显示
    updateStationDisplay(stationData) {
      if (!this.stationSource || !stationData || !Array.isArray(stationData)) {
        console.log('站点数据无效或站点图层未初始化')
        return
      }

      // 保存原始站点数据
      this.originalStationData = stationData

      // 根据显示模式过滤站点
      const filteredStations = this.filterStationsByMode(stationData)

      console.log('更新站点显示，原始站点数量:', stationData.length, '过滤后数量:', filteredStations.length, '显示模式:', this.selectedStationTypes)

      // 清除现有站点
      this.stationSource.clear()

      // 添加过滤后的站点
      filteredStations.forEach((station, index) => {
        this.addStationToMap(station, index)
      })

      console.log('站点显示更新完成，当前站点图层特征数量:', this.stationSource.getFeatures().length)
    },

    // 添加站点到地图，参考baseMap.vue的实现
    addStationToMap(station, index) {
      try {
        // 解析站点经纬度
        const lon = parseFloat(station.lon || station.longitude || station.Lon)
        const lat = parseFloat(station.lat || station.latitude || station.Lat)

        if (isNaN(lon) || isNaN(lat)) {
          console.warn('站点坐标无效:', station)
          return
        }

        // 创建几何特征
        const geometry = new Point(fromLonLat([lon, lat]))

        // 创建特征对象
        const feature = new Feature({
          geometry: geometry,
          name: station.stationName,
          id: station.stationIdC,
          level: station.stationLevl,
          city: station.city,
          cnty: station.cnty,
          lon: lon,
          lat: lat
        })

        // 设置特征ID
        feature.setId(station.id || station.stationId || `station_${index}_${Date.now()}`)

        // 添加到数据源
        this.stationSource.addFeature(feature)

        if (index < 5) { // 只显示前5个站点的详细日志，避免日志过多
          console.log(`添加站点[${index}]到地图:`, feature.get('name'), `(${lon}, ${lat})`, '级别:', feature.get('level'))
        }
      } catch (error) {
        console.error('添加站点到地图失败:', error, station)
      }
    },

    // 添加自定义双击处理器
    addCustomDoubleClickHandler() {
      if (!this.map || !this.isDrawing) return

      // 移除之前的监听器
      this.removeCustomDoubleClickHandler()

      // 添加地图点击监听
      this.doubleClickListener = this.map.on('click', (event) => {
        if (!this.isDrawing || !this.drawInteraction) return

        const currentTime = Date.now()
        const timeDiff = currentTime - this.lastClickTime

        // 检测双击（300ms内的两次点击）
        if (timeDiff < 300 && timeDiff > 0) {
          // 阻止事件传播
          event.preventDefault()
          event.stopPropagation()

          // 结束当前绘制
          this.finishCurrentDrawing()
          return false
        }

        this.lastClickTime = currentTime
      })
    },

    // 移除自定义双击处理器
    removeCustomDoubleClickHandler() {
      if (this.doubleClickListener) {
        unByKey(this.doubleClickListener)
        this.doubleClickListener = null
      }
      if (this.clickTimeout) {
        clearTimeout(this.clickTimeout)
        this.clickTimeout = null
      }
    },

    // 完成当前绘制
    finishCurrentDrawing() {
      if (!this.drawInteraction) return

      try {
        // 手动触发绘制完成
        this.drawInteraction.finishDrawing()
      } catch (error) {
        console.warn('手动结束绘制失败:', error)
        // 如果手动结束失败，直接停止绘制
        this.stopDrawing()
      }
    },

    // 添加站点点击事件监听
    addStationClickListener() {
      if (!this.map || !this.stationSource) return

      // 移除之前的监听器
      this.removeStationClickListener()

      // 添加站点点击监听
      this.stationClickListener = this.map.on('click', (event) => {
        // 使用地图的getFeaturesAtPixel方法，并通过layerFilter过滤站点图层
        const features = this.map.getFeaturesAtPixel(event.pixel, {
          layerFilter: (layer) => layer === this.stationLayer
        })

        if (features.length > 0) {
          const feature = features[0]

          // 显示站点信息弹窗
          this.openStationPopup(feature, event.pixel)
        } else {
          // 点击了地图其他地方，关闭弹窗
          this.closeStationPopup()
        }
      })
    },

    // 移除站点点击事件监听
    removeStationClickListener() {
      if (this.stationClickListener) {
        unByKey(this.stationClickListener)
        this.stationClickListener = null
      }
    },

    // 打开站点信息弹窗
    openStationPopup(feature, pixel) {
      this.stationPopupInfo = {
        name: feature.get('name'),
        id: feature.get('id'),
        city: feature.get('city'),
        cnty: feature.get('cnty'),
        level: feature.get('level'),
        lon: feature.get('lon'),
        lat: feature.get('lat')
      }
      this.showStationPopup = true
      this.updateStationPopupPosition(feature, pixel)
    },

    // 关闭站点信息弹窗
    closeStationPopup() {
      this.showStationPopup = false
      this.stationPopupInfo = null
    },

    // 更新站点信息弹窗位置
    updateStationPopupPosition(feature, pixel) {
      if (!pixel) return

      // 获取地图容器的位置和尺寸
      const mapContainer = this.map.getTargetElement()
      const mapRect = mapContainer.getBoundingClientRect()

      // 弹窗尺寸
      const popupWidth = 280
      const popupHeight = 220

      // 基础位置：在点击点右侧稍微偏移
      let x = pixel[0] + 15 // 向右偏移15px
      let y = pixel[1] - popupHeight / 2 // 垂直居中对齐站点

      // 边界检测和调整
      const mapWidth = mapRect.width
      const mapHeight = mapRect.height

      // 如果右侧空间不够，显示在左侧
      if (x + popupWidth > mapWidth - 20) {
        x = pixel[0] - popupWidth - 15
      }

      // 如果上方空间不够，向下调整
      if (y < 20) {
        y = 20
      }

      // 如果下方空间不够，向上调整
      if (y + popupHeight > mapHeight - 20) {
        y = mapHeight - popupHeight - 20
      }

      // 确保不会超出左边界
      if (x < 20) {
        x = 20
      }

      this.stationPopupPosition.x = x
      this.stationPopupPosition.y = y
    },

    // 获取站点级别文本
    getLevelText(level) {
      const levelNum = parseInt(level)
      if (levelNum > 13) {
        return '区域站'
      } else if (levelNum <= 13) {
        return '国家站'
      }
      return `级别${level}`
    },

    // 获取当前显示的站点数量
    getDisplayedStationCount() {
      if (!this.stationData || this.stationData.length === 0) {
        return 0
      }

      const selectedTypes = this.selectedStationTypes

      // 如果没有选择任何类型，返回0
      if (!selectedTypes || selectedTypes.length === 0) {
        return 0
      }

      let count = 0
      this.stationData.forEach(station => {
        const level = parseInt(station.stationLevl)

        if (selectedTypes.includes('national') && level <= 13) {
          count++
        } else if (selectedTypes.includes('regional') && level > 13) {
          count++
        }
      })

      return count
    },

    // 根据显示模式过滤站点
    filterStationsByMode(stationData) {
      if (!stationData || !Array.isArray(stationData)) {
        return []
      }

      const selectedTypes = this.selectedStationTypes

      // 如果没有选择任何类型，不显示任何站点
      if (!selectedTypes || selectedTypes.length === 0) {
        return []
      }

      return stationData.filter(station => {
        const level = parseInt(station.stationLevl)

        // 检查是否符合选中的类型
        if (selectedTypes.includes('national') && level <= 13) {
          return true
        }
        if (selectedTypes.includes('regional') && level > 13) {
          return true
        }

        return false
      })
    },

    // ==================== 文件上传相关方法 ====================

    // 上传前验证
    beforeUpload(file) {
      console.log('准备上传文件:', file.name, '大小:', file.size)

      // 验证文件大小（限制为10MB）
      const maxSize = 10 * 1024 * 1024 // 10MB
      if (file.size > maxSize) {
        this.$message.error('文件大小不能超过10MB')
        return false
      }

      // 更新上传状态
      this.uploading = true
      this.$message.info('开始上传文件...')

      return true
    },

    // 上传成功回调
    onUploadSuccess(response, file) {
      console.log('文件上传成功:', response)
      this.uploading = false

      if (response.code === 0 && response.data) {
        this.$message.success('文件上传成功，正在解析数据...')

        // 解析上传的文件数据
        this.parseUploadedFile(response.data)
      } else {
        this.$message.error(response.msg || '文件上传失败')
      }
    },

    // 上传失败回调
    onUploadError(error, file) {
      console.error('文件上传失败:', error)
      this.uploading = false
      this.$message.error('文件上传失败，请重试')
    },

    // 解析上传的文件数据
    parseUploadedFile(fileData) {
      try {
        if (fileData.parsedData) {
          // 如果后端已经解析了数据，直接使用
          this.loadParsedData(fileData.parsedData)
          this.$message.success('文件解析成功，已加载降水落区数据')
        } else if (fileData.content) {
          // 如果是文件内容，前端解析
          this.parseFileContent(fileData.content)
        } else {
          this.$message.error('文件数据格式不正确')
        }
      } catch (error) {
        console.error('解析文件数据失败:', error)
        this.$message.error('解析文件数据失败：' + error.message)
      }
    },

    // 解析文件内容（类似示例文件格式）
    parseFileContent(content) {
      try {
        console.log('解析文件内容...')
        const lines = content.split('\n')
        const precipitationData = {}

        let currentSection = null
        let currentLevel = null
        let currentCoordinates = []

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim()

          if (line.startsWith('CLOSED_CONTOURS:')) {
            // 开始解析闭合轮廓
            currentSection = 'contours'
            continue
          }

          // 检测站点情况段开始（结束轮廓解析）- 可选字段
          if (line.startsWith('STATION_SITUATION')) {
            currentSection = null
            // 保存最后一个轮廓（如果有的话）
            if (currentLevel && currentCoordinates.length > 0) {
              console.warn('发现没有结束标识的轮廓数据，使用默认量级')
              const defaultLevel = 'level0'
              if (!precipitationData[defaultLevel]) {
                precipitationData[defaultLevel] = []
              }
              precipitationData[defaultLevel].push({
                id: this.generateFeatureId(),
                geometry: {
                  type: 'Polygon',
                  coordinates: [[...currentCoordinates]] // 创建坐标副本
                },
                properties: {
                  precipitationLevel: defaultLevel,
                  createTime: new Date().toISOString()
                }
              })
            }
            break
          }

          if (currentSection === 'contours' && line.match(/^\d+\s+\d+$/)) {
            // 解析数据行 (如: "3 55" 表示3无用，55个坐标点; "100 1" 表示降水量100，1个无用坐标点)
            const [firstNum, pointCount] = line.split(/\s+/).map(Number)

            // 如果点数为1，这是降水量结束标识
            if (pointCount === 1) {
              if (currentLevel && currentCoordinates.length > 0) {
                // 使用结束标识中的降水量值作为真正的量级
                const precipitationLevel = this.mapLevelToString(firstNum)

                if (!precipitationData[precipitationLevel]) {
                  precipitationData[precipitationLevel] = []
                }

                precipitationData[precipitationLevel].push({
                  id: this.generateFeatureId(),
                  geometry: {
                    type: 'Polygon',
                    coordinates: [[...currentCoordinates]] // 创建坐标副本
                  },
                  properties: {
                    precipitationLevel: precipitationLevel,
                    createTime: new Date().toISOString()
                  }
                })

                currentCoordinates = []
                currentLevel = null
              }
              // 跳过下一行的无用坐标数据
              i++
              continue
            }

            // 保存上一个轮廓（如果有的话）
            if (currentLevel && currentCoordinates.length > 0) {
              // 这种情况不应该发生，因为每个轮廓都应该有结束标识
              console.warn('发现没有结束标识的轮廓数据')
            }

            // 开始新轮廓，第一个数字无用，只记录要读取的坐标点数
            currentLevel = 'temp' // 临时标识，真正的量级在结束标识中
            currentCoordinates = []
            continue
          }

          // 检查是否为坐标行
          const coordRegex = /^\s*[\d.-]+\s+[\d.-]+\s+[\d.-]+/
          const isCoordLine = coordRegex.test(line)

          if (currentSection === 'contours' && isCoordLine) {
            // 解析坐标点
            const coords = line.trim().split(/\s+/)
            for (let j = 0; j < coords.length; j += 3) {
              if (j + 1 < coords.length) {
                const lng = parseFloat(coords[j])
                const lat = parseFloat(coords[j + 1])
                if (!isNaN(lng) && !isNaN(lat)) {
                  currentCoordinates.push([lng, lat])
                }
              }
            }
            continue
          }
        }

        // 处理文件结束时的最后一个轮廓（如果有的话）
        if (currentLevel && currentCoordinates.length > 0) {
          console.warn('文件结束时发现没有结束标识的轮廓数据，使用默认量级')
          const defaultLevel = 'level0'
          if (!precipitationData[defaultLevel]) {
            precipitationData[defaultLevel] = []
          }
          precipitationData[defaultLevel].push({
            id: this.generateFeatureId(),
            geometry: {
              type: 'Polygon',
              coordinates: [[...currentCoordinates]] // 创建坐标副本
            },
            properties: {
              precipitationLevel: defaultLevel,
              createTime: new Date().toISOString()
            }
          })
        }

        console.log('解析完成的降水数据:', precipitationData)

        if (Object.keys(precipitationData).length > 0) {
          this.loadParsedData(precipitationData)
          this.$message.success('文件解析成功，已加载降水落区数据')
        } else {
          this.$message.warning('未能从文件中解析出有效的降水落区数据')
        }
      } catch (error) {
        console.error('解析文件内容失败:', error)
        this.$message.error('解析文件内容失败：' + error.message)
      }
    },

    // 将数字量级映射为字符串（level0为小雨，level10为中雨，依次类推）
    mapLevelToString(level) {
      const levelMap = {
        0: 'level0', // level0 - 小雨
        10: 'level10', // level10 - 中雨
        25: 'level25', // level25 - 大雨
        50: 'level50', // level50 - 暴雨
        100: 'level100', // level100 - 大暴雨
        250: 'level250', // level250 - 特大暴雨
        500: 'level500' // level500 - 预留
      }

      return levelMap[level] || `level${level}`
    },

    // 加载解析后的数据
    loadParsedData(parsedData) {
      try {
        // 清除现有数据
        this.precipitationSource.clear()
        this.drawnAreas = {}

        const geoJsonFormat = new GeoJSON()

        // 获取真正的降水数据
        const precipitationData = parsedData.data || parsedData

        if (!precipitationData || typeof precipitationData !== 'object') {
          this.$message.error('降水数据格式不正确')
          return
        }

        // 遍历每个量级的数据
        Object.keys(precipitationData).forEach(level => {
          const areas = precipitationData[level]

          if (Array.isArray(areas)) {
            this.$set(this.drawnAreas, level, [])

            areas.forEach((areaData) => {
              try {
                // 创建地图要素
                const feature = geoJsonFormat.readFeature({
                  type: 'Feature',
                  geometry: areaData.geometry,
                  properties: areaData.properties || {}
                }, {
                  dataProjection: 'EPSG:4326',
                  featureProjection: 'EPSG:3857'
                })

                // 设置要素属性
                feature.set('precipitationLevel', level)
                feature.set('id', areaData.id || this.generateFeatureId())

                // 添加到地图
                this.precipitationSource.addFeature(feature)

                // 添加到数据结构
                this.drawnAreas[level].push(areaData)
              } catch (error) {
                console.error('创建地图要素失败:', error, areaData)
              }
            })
          }
        })

        // 添加修改交互
        this.addModifyInteraction()

        // 触发数据变更事件
        this.emitDataChange()
      } catch (error) {
        console.error('加载解析后的数据失败:', error)
        this.$message.error('加载数据失败：' + error.message)
      }
    }
  }
}
</script>

<style scoped>
.precipitation-drawing {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 15px;
  padding: 15px;
  min-height: 0;
  overflow: hidden;
}

/* 左侧工具栏 */
.left-toolbar {
  width: 280px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  max-height: calc(100vh - 30px);
  overflow-y: auto;
  flex-shrink: 0;
}

/* 工具栏标题 */
.toolbar-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.toolbar-title i {
  font-size: 18px;
  color: #409eff;
}

/* 地图区域 */
.map-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

/* 降水量级选择 */
.precipitation-levels {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.level-title {
  font-size: 14px;
  font-weight: 600;
  color: #34495e;
  margin: 0;
}

.level-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.level-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
  position: relative;
}

.level-button:hover {
  border-color: #409eff;
  background: #ecf5ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.level-button.active {
  border-color: #409eff;
  background: white;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transform: translateY(-2px);
}

.level-button.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #409eff;
  border-radius: 4px 0 0 4px;
}

.color-indicator {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.level-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.level-name {
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.2;
  flex-shrink: 0;
}

.level-range {
  font-size: 11px;
  color: #666;
  line-height: 1.2;
  text-align: right;
  flex-shrink: 0;
}

/* 绘制控制按钮 */
.drawing-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.control-button {
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  font-weight: 500;
  text-align: center !important;
  min-height: 44px;
  box-sizing: border-box;
  margin: 0 !important;
  border: 1px solid transparent !important;
  transition: all 0.3s ease !important;
}

.control-button i {
  font-size: 16px;
  margin-right: 0 !important;
  flex-shrink: 0;
  width: 16px;
  text-align: center;
}

.control-button span {
  text-align: center !important;
  margin-left: 0 !important;
  white-space: nowrap;
  flex: 0;
}

.drawing-active {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(64, 158, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(64, 158, 255, 0); }
}

/* 文件上传组件样式 - 确保与其他按钮完全一致 */
.upload-component {
  width: 100% !important;
  display: block !important;
  margin: 0 !important;
}

.upload-component .el-upload {
  width: 100% !important;
  display: block !important;
  margin: 0 !important;
}

.upload-component .el-upload .control-button {
  width: 100% !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  font-weight: 500;
  text-align: center !important;
  min-height: 44px;
  box-sizing: border-box;
  border: 1px solid transparent !important;
  transition: all 0.3s ease !important;
}

/* 拖拽上传组件样式 */
.drag-upload-component {
  width: 100% !important;
  display: flex !important;
  justify-content: center !important;
  margin: 0 !important;
  position: relative !important;
}

.drag-upload-component .el-upload {
  width: 100% !important;
  display: block !important;
  margin: 0 !important;
}

.drag-upload-component .el-upload-dragger {
  width: 100% !important;
  max-width: 280px !important;
  height: 100px !important;
  min-height: 100px !important;
  padding: 16px 12px !important;
  margin: 0 auto !important;
  border: 2px dashed #d9d9d9 !important;
  border-radius: 8px !important;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%) !important;
  color: #666 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-sizing: border-box !important;
  position: relative !important;
  cursor: pointer !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
}

.drag-upload-component .el-upload-dragger:hover {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%) !important;
  border-color: #409eff !important;
  color: #409eff !important;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2) !important;
  transform: translateY(-1px) !important;
}

.drag-upload-component .el-upload-dragger.is-dragover {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%) !important;
  border-color: #52c41a !important;
  color: #52c41a !important;
  border-style: solid !important;
  box-shadow: 0 0 0 3px rgba(82, 196, 26, 0.2), 0 4px 20px rgba(82, 196, 26, 0.15) !important;
  transform: translateY(-2px) !important;
}

.drag-upload-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  width: 100% !important;
  height: 100% !important;
}

.upload-icon-wrapper {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
}

.drag-upload-icon {
  font-size: 24px !important;
  color: #bfbfbf !important;
  flex-shrink: 0 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  margin-bottom: 2px !important;
}

.drag-upload-component .el-upload-dragger:hover .drag-upload-icon {
  color: #409eff !important;
  transform: scale(1.1) !important;
}

.drag-upload-text {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 4px !important;
  text-align: center !important;
}

.drag-main-text {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #666 !important;
  line-height: 1.2 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  white-space: nowrap !important;
  margin-bottom: 1px !important;
}

.drag-upload-component .el-upload-dragger:hover .drag-main-text {
  color: #409eff !important;
}

.drag-sub-text {
  font-size: 12px !important;
  color: #999 !important;
  line-height: 1.2 !important;
  font-weight: 400 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  white-space: nowrap !important;
}

.drag-upload-component .el-upload-dragger:hover .drag-sub-text {
  color: #666 !important;
}

.uploading-text {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #409eff !important;
  line-height: 1.2 !important;
  white-space: nowrap !important;
}

/* 拖拽状态下的图标和文字变化 */
.drag-upload-component .el-upload-dragger.is-dragover .drag-upload-icon {
  color: #52c41a !important;
  transform: scale(1.2) !important;
}

.drag-upload-component .el-upload-dragger.is-dragover .drag-main-text {
  color: #52c41a !important;
  transform: translateY(-1px) !important;
}

.drag-upload-component .el-upload-dragger.is-dragover .drag-sub-text {
  color: #73d13d !important;
}

/* 上传中状态样式 */
.drag-upload-component .uploading .el-upload-dragger {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%) !important;
  border-color: #409eff !important;
  color: #409eff !important;
  cursor: not-allowed !important;
}

.drag-upload-component .uploading .drag-upload-icon {
  color: #409eff !important;
  animation: rotate 1s linear infinite !important;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 拖拽上传响应式样式 */
@media (max-width: 768px) {
  .drag-upload-component .el-upload-dragger {
    max-width: 240px !important;
    height: 80px !important;
    min-height: 80px !important;
    padding: 12px 10px !important;
    gap: 6px !important;
  }

  .drag-upload-icon {
    font-size: 20px !important;
  }

  .drag-main-text {
    font-size: 13px !important;
  }

  .drag-sub-text {
    font-size: 11px !important;
  }

  .drag-upload-content {
    gap: 4px !important;
  }

  .uploading-text {
    font-size: 13px !important;
  }
}

/* 绘制提示 */
.drawing-tip {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #bbdefb;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.drawing-tip i {
  color: #2196f3;
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.tip-text {
  flex: 1;
  font-size: 12px;
  color: #1976d2;
  line-height: 1.4;
}

.tip-text div {
  margin-bottom: 2px;
}

.tip-text div:last-child {
  margin-bottom: 0;
}

/* 统计信息 */
.stats-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
}

.stats-title {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.stats-label {
  color: #6c757d;
}

.stats-value {
  font-weight: 600;
  color: #28a745;
}

/* 站点显示控制 */
.station-control-section {
  margin-top: 0px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.station-control-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.station-control-title i {
  font-size: 16px;
  color: #667eea;
}

.station-control-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.station-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.station-checkbox-group .el-checkbox {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 14px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
  margin: 0;
  margin-bottom: 8px;
}

.station-checkbox-group .el-checkbox:last-child {
  margin-bottom: 0;
}

.station-checkbox-group .el-checkbox:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.station-checkbox-group .el-checkbox.is-checked {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 3px 12px rgba(102, 126, 234, 0.3);
  transform: translateX(4px);
}

.station-checkbox-group .el-checkbox.is-checked::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px 0 0 4px;
}

.checkbox-text {
  font-size: 13px;
  color: #4a5568;
  font-weight: 500;
  line-height: 1.2;
  margin-left: 8px;
}

.station-checkbox-group .el-checkbox.is-checked .checkbox-text {
  color: #667eea;
  font-weight: 600;
}

.station-stats {
  margin-top: 12px;
  padding: 10px 12px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.station-stats .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.station-stats .stat-label {
  color: #6c757d;
  font-size: 13px;
  font-weight: 500;
}

.station-stats .stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
}

/* 地图容器 */
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.map-canvas {
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 30px);
  position: relative;
  background-color: #f5f7fa;
}

/* 确保OpenLayers地图容器正确显示 */
.map-canvas .ol-viewport {
  width: 100% !important;
  height: 100% !important;
}

.map-canvas .ol-overlaycontainer-stopevent {
  width: 100% !important;
  height: 100% !important;
}

.map-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 16px;
  z-index: 1000;
}

.map-loading i {
  font-size: 24px;
  margin-bottom: 10px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.map-legend {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  z-index: 2;
}

.legend-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.legend-color {
  width: 18px;
  height: 18px;
  border-radius: 3px;
  border: 1px solid #ddd;
  flex-shrink: 0;
}

.legend-label {
  font-size: 13px;
  color: #333;
  line-height: 1.4;
}

.drawing-stats {
  margin: 15px;
  margin-top: 0;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px 20px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.drawing-stats .stats-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.drawing-stats .stats-content {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.drawing-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.drawing-stats .stat-label {
  font-size: 13px;
  color: #666;
}

.drawing-stats .stat-value {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.drawing-stats .stat-total {
  display: flex;
  align-items: center;
  gap: 4px;
  padding-left: 15px;
  border-left: 2px solid #409eff;
  margin-left: 10px;
}

.drawing-stats .stat-total .stat-label {
  color: #409eff;
  font-weight: 500;
}

.drawing-stats .stat-total .stat-value {
  color: #409eff;
  font-weight: 600;
}

/* 站点信息弹窗 */
.station-popup {
  position: absolute;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
  border: none;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 1000;
  font-size: 14px;
  color: #333;
  line-height: 1.6;
  width: 280px;
  min-height: 220px;
  pointer-events: auto;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  margin: 0;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

.popup-title::before {
  content: '📍';
  font-size: 14px;
}

.popup-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  color: white;
  transition: all 0.2s ease;
  font-weight: bold;
}

.popup-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.popup-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
  border-left: 3px solid #667eea;
  transition: all 0.2s ease;
}

.info-row:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateX(2px);
}

.info-label {
  font-weight: 600;
  color: #4a5568;
  font-size: 13px;
  min-width: 50px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.info-label::before {
  content: '•';
  color: #667eea;
  font-weight: bold;
}

.info-value {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

/* 特殊样式 */
.info-row:nth-child(1) .info-label::before { content: '🏷️'; }
.info-row:nth-child(2) .info-label::before { content: '🔢'; }
.info-row:nth-child(3) .info-label::before { content: '🏙️'; }
.info-row:nth-child(4) .info-label::before { content: '📍'; }
.info-row:nth-child(5) .info-label::before { content: '⭐'; }
.info-row:nth-child(6) .info-label::before { content: '🌐'; }

/* 弹窗出现动画 */
.station-popup {
  animation: popupFadeIn 0.3s ease-out;
}

@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
