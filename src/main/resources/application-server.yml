# 生产环境配置文件
spring:
  # 数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************
    username: exam
    password: Exam123!@#
    # druid相关配置
    druid:
      max-active: 50
      initial-size: 5
      min-idle: 5
      async-init: true
      # 监控统计
      filters: stat,wall
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 5000
        wall:
          config:
            create-table-allow: false
            alter-table-allow: false
            drop-table-allow: false
            truncate-allow: false


# 开启文档
swagger:
  enable: true

logging:
  level:
    root: info
  path: logs/${spring.application.name}/

conf:
  upload:
    # 物理文件存储位置，以/结束，windows已正斜杠，如：d:/exam-upload/
    dir: /data/upload/
    # 访问地址，注意不要去除/upload/file/，此节点为虚拟标识符
    # 如：http://localhost:8101/upload/file/exam.jpg，对应物理文件为：/data/upload/exam.jpg
    url: http://localhost:8101/upload/file/
    # 允许上传的文件后缀
    allow-extensions: jpg,jpeg,png,dat,txt,nc,grib,grib2,zip,rar,7z,tar,gz,bz2,cma,***************
  # 天气模块文件上传配置
  weather:
    # 天气文件存储基础路径，以/结束
    base-dir: /data/upload/weather/
    # 天气文件访问基础URL
    base-url: http://localhost:8101/upload/file/weather/
