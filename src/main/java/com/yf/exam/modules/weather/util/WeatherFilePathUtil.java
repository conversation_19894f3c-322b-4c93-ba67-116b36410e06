package com.yf.exam.modules.weather.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.File;

/**
 * 天气模块文件路径工具类
 * 
 * 提供静态方法来解析文件路径，解决上传文件路径不一致的问题
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */
@Slf4j
public class WeatherFilePathUtil {

    // 硬编码的天气文件基础路径（与上传控制器保持一致）
    private static final String WEATHER_BASE_DIR = "/data/upload/weather/";

    /**
     * 解析文件路径，将相对路径转换为绝对路径
     * 
     * @param filePath 文件路径（可能是相对路径或绝对路径）
     * @return 绝对路径
     */
    public static String resolveFilePath(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return null;
        }

        // 如果已经是绝对路径，直接返回
        if (isAbsolutePath(filePath)) {
            return filePath;
        }

        // 处理相对路径
        String resolvedPath = resolveRelativePath(filePath);
        
        log.debug("文件路径解析：{} -> {}", filePath, resolvedPath);
        
        return resolvedPath;
    }

    /**
     * 判断是否为绝对路径
     */
    private static boolean isAbsolutePath(String filePath) {
        // Windows路径：C:\ 或 D:\ 等
        if (filePath.matches("^[A-Za-z]:\\\\.*")) {
            return true;
        }
        
        // Unix/Linux路径：以 / 开头
        if (filePath.startsWith("/")) {
            return true;
        }
        
        return false;
    }

    /**
     * 解析相对路径为绝对路径
     */
    private static String resolveRelativePath(String relativePath) {
        // 处理天气模块的相对路径
        if (relativePath.startsWith("weather/")) {
            // 使用硬编码的基础路径
            return WEATHER_BASE_DIR + relativePath.substring("weather/".length());
        }
        
        // 默认使用硬编码路径
        return WEATHER_BASE_DIR + relativePath;
    }

    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    public static boolean fileExists(String filePath) {
        String resolvedPath = resolveFilePath(filePath);
        if (resolvedPath == null) {
            return false;
        }
        
        File file = new File(resolvedPath);
        boolean exists = file.exists();
        
        if (!exists) {
            log.warn("文件不存在：{} (解析后路径：{})", filePath, resolvedPath);
        }
        
        return exists;
    }

    /**
     * 获取文件的绝对路径（如果文件存在）
     * 
     * @param filePath 文件路径
     * @return 绝对路径，如果文件不存在则返回null
     */
    public static String getAbsolutePathIfExists(String filePath) {
        String resolvedPath = resolveFilePath(filePath);
        if (resolvedPath != null && fileExists(filePath)) {
            return resolvedPath;
        }
        return null;
    }

    /**
     * 尝试多种可能的路径来查找文件
     * 
     * @param filePath 原始文件路径
     * @return 找到的文件绝对路径，如果都找不到则返回null
     */
    public static String findFileWithMultiplePaths(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return null;
        }

        // 1. 尝试原始路径
        String resolvedPath = resolveFilePath(filePath);
        if (resolvedPath != null && new File(resolvedPath).exists()) {
            log.debug("文件找到（原始路径）：{}", resolvedPath);
            return resolvedPath;
        }

        // 2. 如果是相对路径，尝试不同的基础路径
        if (!isAbsolutePath(filePath)) {
            // 尝试当前工作目录
            String workingDirPath = System.getProperty("user.dir") + "/" + filePath;
            if (new File(workingDirPath).exists()) {
                log.debug("文件找到（工作目录）：{}", workingDirPath);
                return workingDirPath;
            }

            // 尝试类路径下的资源目录
            String resourcePath = "src/main/resources/" + filePath;
            if (new File(resourcePath).exists()) {
                log.debug("文件找到（资源目录）：{}", resourcePath);
                return resourcePath;
            }

            // 尝试测试资源目录
            String testResourcePath = "src/test/resources/" + filePath;
            if (new File(testResourcePath).exists()) {
                log.debug("文件找到（测试资源目录）：{}", testResourcePath);
                return testResourcePath;
            }
        }

        log.warn("文件未找到，尝试了多种路径：{}", filePath);
        return null;
    }

    /**
     * 获取天气文件的基础目录
     */
    public static String getWeatherBaseDir() {
        return WEATHER_BASE_DIR;
    }

    /**
     * 获取指定类型文件的目录
     */
    public static String getWeatherFileDir(String fileType) {
        switch (fileType.toLowerCase()) {
            case "micaps":
                return WEATHER_BASE_DIR + "micaps/";
            case "observation":
                return WEATHER_BASE_DIR + "observation/";
            case "data":
                return WEATHER_BASE_DIR + "data/";
            case "precipitation-area":
                return WEATHER_BASE_DIR + "precipitation-area/";
            default:
                return WEATHER_BASE_DIR;
        }
    }
}
