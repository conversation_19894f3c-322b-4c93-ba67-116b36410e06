package com.yf.exam.modules.weather.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 天气模块文件上传配置
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "conf.weather")
public class WeatherUploadConfig {

    /**
     * 天气文件存储基础路径，以/结束
     */
    private String baseDir;

    /**
     * 天气文件访问基础URL
     */
    private String baseUrl;

    /**
     * 获取MICAPS文件存储目录
     */
    public String getMicapsDir() {
        return baseDir + "micaps/";
    }

    /**
     * 获取实况文件存储目录
     */
    public String getObservationDir() {
        return baseDir + "observation/";
    }

    /**
     * 获取数据文件存储目录
     */
    public String getDataDir() {
        return baseDir + "data/";
    }

    /**
     * 获取降水落区文件存储目录
     */
    public String getPrecipitationAreaDir() {
        return baseDir + "precipitation-area/";
    }

    /**
     * 获取MICAPS文件访问URL前缀
     */
    public String getMicapsUrl() {
        return baseUrl + "micaps/";
    }

    /**
     * 获取实况文件访问URL前缀
     */
    public String getObservationUrl() {
        return baseUrl + "observation/";
    }

    /**
     * 获取数据文件访问URL前缀
     */
    public String getDataUrl() {
        return baseUrl + "data/";
    }

    /**
     * 获取降水落区文件访问URL前缀
     */
    public String getPrecipitationAreaUrl() {
        return baseUrl + "precipitation-area/";
    }
}
