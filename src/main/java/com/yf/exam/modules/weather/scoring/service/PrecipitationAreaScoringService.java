package com.yf.exam.modules.weather.scoring.service;

import com.yf.exam.modules.station.service.ElStationService;
import com.yf.exam.modules.station.dto.response.ElStationRespDTO;
import com.yf.exam.modules.station.dto.request.ElStationRegionReqDTO;
import com.yf.exam.modules.weather.micaps.MicapsData;
import com.yf.exam.modules.weather.micaps.MicapsDataService;
import com.yf.exam.modules.weather.micaps.MicapsType1Data;
import com.yf.exam.modules.weather.micaps.MicapsType3Data;
import com.yf.exam.modules.weather.micaps.MicapsType4Data;
import com.yf.exam.modules.weather.micaps.MicapsStation;
import com.yf.exam.modules.weather.scoring.dto.PrecipitationScoringResult;
import com.yf.exam.modules.weather.scoring.dto.StationPrecipitationData;
import com.yf.exam.modules.weather.scoring.dto.StationScoringDetail;
import com.yf.exam.modules.weather.scoring.dto.LevelTSScoringDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 历史个例降水落区评分服务
 * 
 * 根据评分细节.md中的历史个例降水落区评分细节实现具体业务：
 * 1. 解析实况降水文件（MICAPS第一类文件）
 * 2. 解析CMA-MESO文件（MICAPS第四类文件）
 * 3. 解析考生答案（JSON降水落区）
 * 4. 计算晴雨TS评分和降水分级TS评分
 * 5. 计算技巧评分和最终评分
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@Service
public class PrecipitationAreaScoringService {

    @Autowired
    private MicapsDataService micapsDataService;

    @Autowired
    private ElStationService stationService;

    /**
     * 计算降水落区评分（向后兼容版本）
     *
     * @param actualFilePath 实况降水文件路径（MICAPS第一类文件）
     * @param cmaMesoFilePath CMA-MESO文件路径（MICAPS第四类文件）
     * @param studentAnswer 考生答案（JSON降水落区）
     * @return 评分结果
     */
    public PrecipitationScoringResult calculatePrecipitationScore(
            String actualFilePath,
            String cmaMesoFilePath,
            Map<String, Object> studentAnswer) {
        return calculatePrecipitationScore(actualFilePath, cmaMesoFilePath, studentAnswer, null);
    }

    /**
     * 计算降水落区评分（带区域筛选）
     *
     * @param actualFilePath 实况降水文件路径（MICAPS第一类文件）
     * @param cmaMesoFilePath CMA-MESO文件路径（MICAPS第四类文件）
     * @param studentAnswer 考生答案（JSON降水落区）
     * @param regionCode 区域编码（1-9），用于筛选对应区域的站点
     * @return 评分结果
     */
    public PrecipitationScoringResult calculatePrecipitationScore(
            String actualFilePath,
            String cmaMesoFilePath,
            Map<String, Object> studentAnswer,
            String regionCode) {
        
        log.info("开始计算降水落区评分，实况文件：{}，CMA文件：{}", actualFilePath, cmaMesoFilePath);
        
        try {
            // 1. 解析实况降水数据（MICAPS第三类文件）
            List<StationPrecipitationData> actualData = parseActualPrecipitationData(actualFilePath);
            log.info("解析实况降水数据完成，站点数：{}", actualData.size());

            // 2. 如果提供了区域编码，则筛选对应区域的站点
            if (regionCode != null && !regionCode.trim().isEmpty()) {
                actualData = filterStationsByRegion(actualData, regionCode);
                log.info("根据区域编码{}筛选后，站点数：{}", regionCode, actualData.size());
            }

            // 3. 解析CMA-MESO预报数据（MICAPS第四类文件）
            List<StationPrecipitationData> cmaMesoData = parseCmaMesoData(cmaMesoFilePath, actualData);
            log.info("解析CMA-MESO数据完成，站点数：{}", cmaMesoData.size());

            // 4. 解析考生答案数据
            List<StationPrecipitationData> studentData = parseStudentAnswer(studentAnswer, actualData);
            log.info("解析考生答案完成，站点数：{}", studentData.size());

            // 5. 计算各量级TS评分（带详细信息）
            List<LevelTSScoringDetail> levelTSDetails = new ArrayList<>();
            Map<String, Double> studentTSScores = calculateAllLevelTSWithDetails(studentData, actualData, levelTSDetails, true, "student");
            Map<String, Double> cmaMesoTSScores = calculateAllLevelTSWithDetails(cmaMesoData, actualData, levelTSDetails, true, "cmaMeso");
            log.info("TS评分计算完成，学生：{}，CMA-MESO：{}", studentTSScores, cmaMesoTSScores);

            // 6. 生成站点详细信息
            List<StationScoringDetail> stationDetails = generateStationDetails(actualData, studentData, cmaMesoData);

            // 7. 计算基础分
            Map<String, Double> baseScores = calculateBaseScores(studentTSScores, cmaMesoTSScores);
            log.info("基础分计算完成：{}", baseScores);

            // 8. 计算技巧评分
            Map<String, Double> skillScores = calculateSkillScores(studentTSScores, baseScores);
            log.info("技巧评分计算完成：{}", skillScores);

            // 9. 生成meso详细记录
            Map<String, Object> mesoRainNoRainDetails = generateMesoRainNoRainDetails(cmaMesoData, actualData);
            Map<String, Object> mesoLevelTSDetails = generateMesoLevelTSDetails(cmaMesoData, actualData, cmaMesoTSScores);

            // 10. 生成考生详细记录（类似meso的设计）
            Map<String, Object> studentRainNoRainDetails = generateStudentRainNoRainDetails(studentData, actualData);
            Map<String, Object> studentLevelTSDetails = generateStudentLevelTSDetails(studentData, actualData, studentTSScores);

            // 11. 将MESO晴雨TS评分详情集成到各量级评分中
            integrateMesoDetailsIntoLevelScoring(levelTSDetails, mesoRainNoRainDetails, mesoLevelTSDetails);

            // 12. 更新量级详情中的评分信息（基础分和技能分放在MESO评分下面）
            updateLevelDetailsWithScores(levelTSDetails, baseScores, skillScores);

            // 13. 计算最终评分
            double finalScore = calculateWeightedFinalScore(skillScores);

            // 12. 构建评分结果
            PrecipitationScoringResult result = new PrecipitationScoringResult();
            result.setStudentTSScores(studentTSScores);
            result.setCmaMesoTSScores(cmaMesoTSScores);
            result.setBaseScores(baseScores);
            result.setSkillScores(skillScores);
            result.setFinalScore(finalScore);
            result.setTotalStations(actualData.size());
            result.setStationDetails(stationDetails);
            result.setLevelTSDetails(levelTSDetails);
            result.setWeights(getWeightsMap());
            result.setMesoRainNoRainDetails(mesoRainNoRainDetails);
            result.setMesoLevelTSDetails(mesoLevelTSDetails);
            result.setStudentRainNoRainDetails(studentRainNoRainDetails);
            result.setStudentLevelTSDetails(studentLevelTSDetails);
            result.setSuccess(true);
            result.setMessage("降水落区评分计算完成");

            log.info("降水落区评分计算完成，最终得分：{}", finalScore);
            return result;

        } catch (Exception e) {
            log.error("降水落区评分计算失败", e);
            PrecipitationScoringResult result = new PrecipitationScoringResult();
            result.setSuccess(false);
            result.setMessage("评分计算失败：" + e.getMessage());
            return result;
        }
    }

    /**
     * 解析实况降水数据（支持MICAPS第一类和第三类文件）
     */
    private List<StationPrecipitationData> parseActualPrecipitationData(String filePath) throws IOException {
        MicapsData micapsData = micapsDataService.parseMicapsFile(filePath);

        // 根据实际数据类型选择处理方式
        if (micapsData instanceof MicapsType1Data) {
            // 第一类数据：标准站点数据
            return parseActualFromType1Data((MicapsType1Data) micapsData);
        } else if (micapsData instanceof MicapsType3Data) {
            // 第三类数据：非规范站点填图数据
            return parseActualFromType3Data((MicapsType3Data) micapsData);
        } else {
            log.warn("不支持的实况数据类型: {}, 返回空数据", micapsData.getClass().getSimpleName());
            return new ArrayList<>();
        }
    }

    /**
     * 从第一类数据中解析实况降水数据
     */
    private List<StationPrecipitationData> parseActualFromType1Data(MicapsType1Data micapsData) {
        return micapsData.getStations().stream()
                .map(station -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(station.getStationId());
                    data.setLongitude(station.getLongitude());
                    data.setLatitude(station.getLatitude());

                    // 从MICAPS站点数据中获取降水量
                    Double precipitation = station.getPrecipitation6h();
                    data.setActualPrecipitation(precipitation != null ? precipitation : 0.0);
                    data.setActualLevel(classifyPrecipitationLevel(data.getActualPrecipitation()));

                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 从第三类数据中解析实况降水数据
     */
    private List<StationPrecipitationData> parseActualFromType3Data(MicapsType3Data micapsData) {
        return micapsData.getStations().stream()
                .map(station -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(station.getStationId());
                    data.setLongitude(station.getLongitude());
                    data.setLatitude(station.getLatitude());

                    // 从第三类站点数据中提取降水量
                    Double precipitation = extractPrecipitationFromType3Station(station);
                    data.setActualPrecipitation(precipitation != null ? precipitation : 0.0);
                    data.setActualLevel(classifyPrecipitationLevel(data.getActualPrecipitation()));

                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 解析CMA-MESO预报数据（支持MICAPS第三类和第四类文件）
     */
    private List<StationPrecipitationData> parseCmaMesoData(String filePath,
                                                           List<StationPrecipitationData> actualData) throws IOException {
        MicapsData micapsData = micapsDataService.parseMicapsFile(filePath);

        // 根据实际数据类型选择处理方式
        if (micapsData instanceof MicapsType4Data) {
            // 第四类数据：格点数据，需要插值
            return parseCmaMesoFromGridData((MicapsType4Data) micapsData, actualData);
        } else if (micapsData instanceof MicapsType3Data) {
            // 第三类数据：站点数据，直接匹配
            return parseCmaMesoFromStationData((MicapsType3Data) micapsData, actualData);
        } else if (micapsData instanceof MicapsType1Data) {
            // 第一类数据：站点数据，直接匹配
            return parseCmaMesoFromType1Data((MicapsType1Data) micapsData, actualData);
        } else {
            log.warn("不支持的MICAPS数据类型: {}, 返回空预报数据", micapsData.getClass().getSimpleName());
            return createEmptyForecastData(actualData);
        }
    }

    /**
     * 从第四类格点数据中解析CMA-MESO预报数据
     * 改进版本：添加详细的日志记录和数据验证
     */
    private List<StationPrecipitationData> parseCmaMesoFromGridData(MicapsType4Data gridData,
                                                                   List<StationPrecipitationData> actualData) {
        log.info("开始从第四类格点数据中插值获取站点预报数据");
        log.info("格点数据信息: {}×{} 格点，范围 [{:.3f}-{:.3f}°E, {:.3f}-{:.3f}°N]，间距 [{:.3f}°, {:.3f}°]",
            gridData.getLonGridNum(), gridData.getLatGridNum(),
            gridData.getStartLon(), gridData.getEndLon(),
            gridData.getStartLat(), gridData.getEndLat(),
            gridData.getLonInterval(), gridData.getLatInterval());

        // 修正纬度范围顺序（如果startLat > endLat则交换坐标系统）
        if (gridData.getStartLat() > gridData.getEndLat()) {
            log.info("检测到纬度范围需要修正，正在自动修正坐标系统");
            gridData.fixLatitudeOrder();
            log.info("纬度范围已修正：startLat({:.3f}) -> endLat({:.3f})",
                gridData.getStartLat(), gridData.getEndLat());
        }
        log.info("需要插值的站点数量: {}", actualData.size());

        int successCount = 0;
        int outOfRangeCount = 0;

        List<StationPrecipitationData> result = actualData.stream()
                .map(actualStation -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(actualStation.getStationId());
                    data.setLongitude(actualStation.getLongitude());
                    data.setLatitude(actualStation.getLatitude());
                    data.setActualPrecipitation(actualStation.getActualPrecipitation());
                    data.setActualLevel(actualStation.getActualLevel());

                    // 从格点数据中插值获取该站点的预报降水量
                    Double forecastPrecipitation = interpolateGridValue(gridData,
                            actualStation.getLongitude(), actualStation.getLatitude());

                    if (forecastPrecipitation != null && forecastPrecipitation > 0) {
                        // 成功插值且有降水
                        data.setForecastPrecipitation(forecastPrecipitation);
                        log.debug("站点 {} ({:.3f}, {:.3f}) 插值成功: {:.2f}mm",
                            actualStation.getStationId(), actualStation.getLongitude(),
                            actualStation.getLatitude(), forecastPrecipitation);
                    } else {
                        // 插值失败或无降水
                        data.setForecastPrecipitation(0.0);
                        if (actualStation.getLongitude() < gridData.getStartLon() ||
                            actualStation.getLongitude() > gridData.getEndLon() ||
                            actualStation.getLatitude() < gridData.getStartLat() ||
                            actualStation.getLatitude() > gridData.getEndLat()) {
                            log.debug("站点 {} ({:.3f}, {:.3f}) 超出格点数据范围",
                                actualStation.getStationId(), actualStation.getLongitude(),
                                actualStation.getLatitude());
                        }
                    }

                    data.setForecastLevel(classifyPrecipitationLevel(data.getForecastPrecipitation()));
                    return data;
                })
                .collect(Collectors.toList());

        // 统计插值结果
        successCount = (int) result.stream().filter(d -> d.getForecastPrecipitation() > 0).count();
        outOfRangeCount = actualData.size() - successCount;

        log.info("格点数据插值完成: 成功插值 {} 个站点，{} 个站点无降水或超出范围",
            successCount, outOfRangeCount);

        return result;
    }

    /**
     * 将格点数据插值到指定站点列表
     * 通用方法，可用于任何需要将格点数据转换为站点数据的场景
     *
     * @param gridData 格点数据
     * @param stations 站点列表（包含经纬度信息）
     * @return 插值后的站点数据映射 (stationId -> precipitation)
     */
    public Map<Long, Double> interpolateGridToStations(MicapsType4Data gridData,
                                                      List<StationPrecipitationData> stations) {
        if (gridData == null || stations == null || stations.isEmpty()) {
            log.warn("格点数据或站点列表为空，无法进行插值");
            return Collections.emptyMap();
        }

        log.info("开始将格点数据插值到 {} 个站点", stations.size());

        Map<Long, Double> result = new HashMap<>();
        int successCount = 0;

        for (StationPrecipitationData station : stations) {
            Double value = interpolateGridValue(gridData, station.getLongitude(), station.getLatitude());
            result.put(station.getStationId(), value != null ? value : 0.0);

            if (value != null && value > 0) {
                successCount++;
            }
        }

        log.info("格点插值完成: {} / {} 个站点有有效数据", successCount, stations.size());
        return result;
    }

    /**
     * 从第三类站点数据中解析CMA-MESO预报数据
     */
    private List<StationPrecipitationData> parseCmaMesoFromStationData(MicapsType3Data stationData,
                                                                      List<StationPrecipitationData> actualData) {
        // 创建站点ID到预报数据的映射
        Map<Long, MicapsType3Data.MicapsType3Station> forecastStationMap = stationData.getStations().stream()
                .collect(Collectors.toMap(
                    MicapsType3Data.MicapsType3Station::getStationId,
                    station -> station,
                    (existing, replacement) -> existing // 如果有重复ID，保留第一个
                ));

        return actualData.stream()
                .map(actualStation -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(actualStation.getStationId());
                    data.setLongitude(actualStation.getLongitude());
                    data.setLatitude(actualStation.getLatitude());
                    data.setActualPrecipitation(actualStation.getActualPrecipitation());
                    data.setActualLevel(actualStation.getActualLevel());

                    // 查找对应的预报站点数据
                    MicapsType3Data.MicapsType3Station forecastStation = forecastStationMap.get(actualStation.getStationId());
                    if (forecastStation != null) {
                        // 尝试从站点值中获取降水量
                        Double forecastPrecipitation = extractPrecipitationFromType3Station(forecastStation);
                        data.setForecastPrecipitation(forecastPrecipitation != null ? forecastPrecipitation : 0.0);
                    } else {
                        // 如果没有找到对应站点，尝试通过距离最近的站点插值
                        Double forecastPrecipitation = findNearestStationType3Value(stationData,
                                actualStation.getLongitude(), actualStation.getLatitude());
                        data.setForecastPrecipitation(forecastPrecipitation != null ? forecastPrecipitation : 0.0);
                    }

                    data.setForecastLevel(classifyPrecipitationLevel(data.getForecastPrecipitation()));
                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 从第一类站点数据中解析CMA-MESO预报数据
     */
    private List<StationPrecipitationData> parseCmaMesoFromType1Data(MicapsType1Data stationData,
                                                                    List<StationPrecipitationData> actualData) {
        // 创建站点ID到预报数据的映射
        Map<Long, MicapsStation> forecastStationMap = stationData.getStations().stream()
                .collect(Collectors.toMap(
                    MicapsStation::getStationId,
                    station -> station,
                    (existing, replacement) -> existing // 如果有重复ID，保留第一个
                ));

        return actualData.stream()
                .map(actualStation -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(actualStation.getStationId());
                    data.setLongitude(actualStation.getLongitude());
                    data.setLatitude(actualStation.getLatitude());
                    data.setActualPrecipitation(actualStation.getActualPrecipitation());
                    data.setActualLevel(actualStation.getActualLevel());

                    // 查找对应的预报站点数据
                    MicapsStation forecastStation = forecastStationMap.get(actualStation.getStationId());
                    if (forecastStation != null) {
                        // 从降水字段获取预报降水量
                        Double forecastPrecipitation = forecastStation.getPrecipitation6h();
                        data.setForecastPrecipitation(forecastPrecipitation != null ? forecastPrecipitation : 0.0);
                    } else {
                        // 如果没有找到对应站点，尝试通过距离最近的站点插值
                        Double forecastPrecipitation = findNearestStationType1Value(stationData,
                                actualStation.getLongitude(), actualStation.getLatitude());
                        data.setForecastPrecipitation(forecastPrecipitation != null ? forecastPrecipitation : 0.0);
                    }

                    data.setForecastLevel(classifyPrecipitationLevel(data.getForecastPrecipitation()));
                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 解析考生答案数据
     */
    private List<StationPrecipitationData> parseStudentAnswer(Map<String, Object> studentAnswer, 
                                                             List<StationPrecipitationData> actualData) {
        return actualData.stream()
                .map(actualStation -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(actualStation.getStationId());
                    data.setLongitude(actualStation.getLongitude());
                    data.setLatitude(actualStation.getLatitude());
                    data.setActualPrecipitation(actualStation.getActualPrecipitation());
                    data.setActualLevel(actualStation.getActualLevel());
                    
                    // 判断该站点是否在考生绘制的降水落区内
                    String forecastLevel = determineStationForecastLevel(studentAnswer, 
                            actualStation.getLongitude(), actualStation.getLatitude());
                    data.setForecastLevel(forecastLevel);
                    data.setForecastPrecipitation(convertLevelToPrecipitation(forecastLevel));
                    
                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建空的预报数据（当无法解析预报文件时使用）
     */
    private List<StationPrecipitationData> createEmptyForecastData(List<StationPrecipitationData> actualData) {
        return actualData.stream()
                .map(actualStation -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(actualStation.getStationId());
                    data.setLongitude(actualStation.getLongitude());
                    data.setLatitude(actualStation.getLatitude());
                    data.setActualPrecipitation(actualStation.getActualPrecipitation());
                    data.setActualLevel(actualStation.getActualLevel());
                    data.setForecastPrecipitation(0.0);
                    data.setForecastLevel("无降水");
                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 从第三类站点数据中提取降水量
     */
    private Double extractPrecipitationFromType3Station(MicapsType3Data.MicapsType3Station station) {
        // 尝试从value1获取降水量
        Double value1 = parseType3PrecipitationValue(station.getValue1());
        if (value1 != null && value1 >= 0) {
            return value1;
        }

        // 尝试从value2获取降水量
        Double value2 = parseType3PrecipitationValue(station.getValue2());
        if (value2 != null && value2 >= 0) {
            return value2;
        }

        return 0.0;
    }

    /**
     * 解析第三类数据中的降水量字符串值
     */
    private Double parseType3PrecipitationValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        String trimmedValue = value.trim();

        // 特殊处理微量降水标识"T"
        if ("T".equalsIgnoreCase(trimmedValue) || "t".equals(trimmedValue)) {
            return 0.001; // T表示微量降水（Trace）
        }

        // 处理其他特殊标识
        if ("0".equals(trimmedValue) || "无".equals(trimmedValue) || "无降水".equals(trimmedValue)) {
            return 0.0;
        }

        try {
            return Double.parseDouble(trimmedValue);
        } catch (NumberFormatException e) {
            log.debug("无法解析第三类数据降水量值: {}", value);
            return null;
        }
    }

    /**
     * 查找距离最近的第三类站点数值
     */
    private Double findNearestStationType3Value(MicapsType3Data stationData, double targetLon, double targetLat) {
        double minDistance = Double.MAX_VALUE;
        Double nearestValue = null;

        for (MicapsType3Data.MicapsType3Station station : stationData.getStations()) {
            double distance = calculateDistance(targetLon, targetLat,
                    station.getLongitude(), station.getLatitude());

            if (distance < minDistance) {
                Double value = extractPrecipitationFromType3Station(station);
                if (value != null) {
                    minDistance = distance;
                    nearestValue = value;
                }
            }
        }

        return nearestValue;
    }

    /**
     * 查找距离最近的第一类站点数值
     */
    private Double findNearestStationType1Value(MicapsType1Data stationData, double targetLon, double targetLat) {
        double minDistance = Double.MAX_VALUE;
        Double nearestValue = null;

        for (MicapsStation station : stationData.getStations()) {
            double distance = calculateDistance(targetLon, targetLat,
                    station.getLongitude(), station.getLatitude());

            if (distance < minDistance) {
                Double value = station.getPrecipitation6h();
                if (value != null) {
                    minDistance = distance;
                    nearestValue = value;
                }
            }
        }

        return nearestValue;
    }

    /**
     * 计算两点间的距离（简化的球面距离）
     */
    private double calculateDistance(double lon1, double lat1, double lon2, double lat2) {
        double deltaLon = lon2 - lon1;
        double deltaLat = lat2 - lat1;
        return Math.sqrt(deltaLon * deltaLon + deltaLat * deltaLat);
    }

    /**
     * 从格点数据中插值获取指定位置的数值
     * 改进版本：支持更好的错误处理和日志记录
     */
    private Double interpolateGridValue(MicapsType4Data gridData, double lon, double lat) {
        try {
            // 检查格点数据是否有效
            if (gridData == null) {
                log.warn("格点数据为空，无法进行插值");
                return 0.0;
            }

            if (gridData.getGridValues() == null || gridData.getGridValues().isEmpty()) {
                log.warn("格点数据值为空，无法进行插值");
                return 0.0;
            }

            // 检查位置是否在数据范围内
            if (lon < gridData.getStartLon() || lon > gridData.getEndLon() ||
                lat < gridData.getStartLat() || lat > gridData.getEndLat()) {
                log.debug("位置({:.3f}, {:.3f})超出格点数据范围[{:.3f}-{:.3f}, {:.3f}-{:.3f}]",
                    lon, lat, gridData.getStartLon(), gridData.getEndLon(),
                    gridData.getStartLat(), gridData.getEndLat());
                return 0.0;
            }

            // 使用MicapsType4Data内置的双线性插值方法
            Double value = gridData.getValueAtPosition(lon, lat);

            if (value != null) {
                log.debug("位置({:.3f}, {:.3f})插值结果: {:.2f}", lon, lat, value);
                return value;
            } else {
                log.debug("位置({:.3f}, {:.3f})插值返回null，使用默认值0.0", lon, lat);
                return 0.0;
            }

        } catch (Exception e) {
            log.warn("格点数据插值失败，位置：({:.3f}, {:.3f})，错误：{}", lon, lat, e.getMessage());
            return 0.0;
        }
    }

    /**
     * 判断站点在考生绘制的降水落区中的预报等级
     */
    private String determineStationForecastLevel(Map<String, Object> studentAnswer, double lon, double lat) {
        try {
            // 获取考生绘制的降水落区数据
            Map<String, Object> content = (Map<String, Object>) studentAnswer.get("content");
            if (content == null) {
                return "无雨";
            }

            // 获取areas数据
            Map<String, Object> areasData = (Map<String, Object>) content.get("areas");
            if (areasData == null || areasData.isEmpty()) {
                return "无雨";
            }

            // 按优先级顺序检查各降水等级（从高到低）
            // level25对应大暴雨，level10对应暴雨，level5对应大雨，level2对应中雨，level0对应小雨
            String[] levelKeys = {"level100", "level50", "level25", "level10", "level0"};
            String[] levelNames = {"大暴雨", "暴雨", "大雨", "中雨", "小雨"};

            for (int i = 0; i < levelKeys.length; i++) {
                String levelKey = levelKeys[i];
                String levelName = levelNames[i];

                if (areasData.containsKey(levelKey)) {
                    List<Map<String, Object>> areas = (List<Map<String, Object>>) areasData.get(levelKey);
                    if (areas != null && !areas.isEmpty()) {
                        for (Map<String, Object> area : areas) {
                            if (isPointInArea(lon, lat, area)) {
                                return levelName;
                            }
                        }
                    }
                }
            }

            return "无雨";
        } catch (Exception e) {
            log.warn("判断站点降水等级失败，位置：({}, {})，错误：{}", lon, lat, e.getMessage());
            return "无雨";
        }
    }

    /**
     * 判断点是否在区域内（使用射线法）
     */
    private boolean isPointInArea(double lon, double lat, Map<String, Object> area) {
        try {
            Map<String, Object> geometry = (Map<String, Object>) area.get("geometry");
            if (geometry == null || !"Polygon".equals(geometry.get("type"))) {
                return false;
            }

            List<List<List<Double>>> coordinates = (List<List<List<Double>>>) geometry.get("coordinates");
            if (coordinates == null || coordinates.isEmpty()) {
                return false;
            }

            List<List<Double>> polygon = coordinates.get(0); // 取外环
            return isPointInPolygon(lon, lat, polygon);
        } catch (Exception e) {
            log.warn("判断点是否在区域内失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 使用射线法判断点是否在多边形内
     */
    private boolean isPointInPolygon(double lon, double lat, List<List<Double>> polygon) {
        int intersections = 0;
        int n = polygon.size();
        
        for (int i = 0; i < n - 1; i++) {
            List<Double> p1 = polygon.get(i);
            List<Double> p2 = polygon.get(i + 1);
            
            double x1 = p1.get(0), y1 = p1.get(1);
            double x2 = p2.get(0), y2 = p2.get(1);
            
            // 检查射线是否与边相交
            if (((y1 > lat) != (y2 > lat)) && 
                (lon < (x2 - x1) * (lat - y1) / (y2 - y1) + x1)) {
                intersections++;
            }
        }
        
        return (intersections % 2) == 1;
    }

    /**
     * 降水等级分类
     */
    private String classifyPrecipitationLevel(double precipitation) {
        // 特殊处理微量降水（0.001表示微量降水）
        if (Math.abs(precipitation - 0.001) < 0.0001) {
            return "微量降水";
        }

        if (precipitation < 0.1) {
            return "无雨";
        } else if (precipitation < 10.0) {
            return "小雨";
        } else if (precipitation < 25.0) {
            return "中雨";
        } else if (precipitation < 50.0) {
            return "大雨";
        } else if (precipitation < 100.0) {
            return "暴雨";
        } else {
            return "大暴雨";
        }
    }

    /**
     * 将降水等级转换为降水量（用于统计）
     */
    private double convertLevelToPrecipitation(String level) {
        switch (level) {
            case "小雨": return 5.0;
            case "中雨": return 17.5;
            case "大雨": return 37.5;
            case "暴雨": return 75.0;
            case "大暴雨": return 150.0;
            default: return 0.0;
        }
    }

    /**
     * 计算所有量级的TS评分
     */
    private Map<String, Double> calculateAllLevelTS(List<StationPrecipitationData> forecastData,
                                                   List<StationPrecipitationData> actualData) {
        Map<String, Double> tsScores = new HashMap<>();

        // 晴雨TS评分
        tsScores.put("晴雨", calculateRainNoRainTS(forecastData, actualData));

        // 各量级TS评分（微量降水不单独评分，作为特殊状态处理）
        tsScores.put("小雨", calculateLevelTS(forecastData, actualData, "小雨"));
        tsScores.put("中雨", calculateLevelTS(forecastData, actualData, "中雨"));
        tsScores.put("大雨", calculateLevelTS(forecastData, actualData, "大雨"));
        tsScores.put("暴雨", calculateLevelTS(forecastData, actualData, "暴雨"));
        tsScores.put("大暴雨", calculateLevelTS(forecastData, actualData, "大暴雨"));

        return tsScores;
    }

    /**
     * 计算晴雨TS评分
     */
    private double calculateRainNoRainTS(List<StationPrecipitationData> forecastData,
                                        List<StationPrecipitationData> actualData) {
        int A = 0, B = 0, C = 0, D = 0;

        for (int i = 0; i < actualData.size(); i++) {
            StationPrecipitationData actual = actualData.get(i);
            StationPrecipitationData forecast = forecastData.get(i);

            String actualLevel = actual.getActualLevel();
            String forecastLevel = forecast.getForecastLevel();

            // 微量降水特殊处理：实况为微量降水时，预报无雨、小雨或微量降水都算正确
            if ("微量降水".equals(actualLevel)) {
                if ("无雨".equals(forecastLevel) || "小雨".equals(forecastLevel) || "微量降水".equals(forecastLevel)) {
                    A++; // 正确预报有降水（微量降水特殊规则）
                } else {
                    B++; // 空报（预报为中雨及以上）
                }
            } else {
                // 常规晴雨评分逻辑
                boolean actualRain = actual.hasActualRain();
                boolean forecastRain = forecast.hasForecastRain();

                if (forecastRain && actualRain) {
                    A++; // 正确预报有降水
                } else if (forecastRain && !actualRain) {
                    B++; // 空报
                } else if (!forecastRain && actualRain) {
                    C++; // 漏报
                } else {
                    D++; // 正确预报无降水
                }
            }
        }

        int total = A + B + C + D;
        return total > 0 ? (double)(A + D) / total : 0.0;
    }

    /**
     * 计算指定量级的TS评分
     */
    private double calculateLevelTS(List<StationPrecipitationData> forecastData,
                                   List<StationPrecipitationData> actualData,
                                   String level) {
        int A = 0, B = 0, C = 0;

        for (int i = 0; i < actualData.size(); i++) {
            StationPrecipitationData actual = actualData.get(i);
            StationPrecipitationData forecast = forecastData.get(i);

            String actualLevel = actual.getActualLevel();
            String forecastLevel = forecast.getForecastLevel();

            // 处理实况为该量级的站点
            if (level.equals(actualLevel)) {
                if (level.equals(forecastLevel)) {
                    A++; // 正确预报该量级
                } else if (!"无雨".equals(forecastLevel)) {
                    B++; // 预报为其他量级
                } else {
                    C++; // 漏报（预报无雨）
                }
            }
            // 处理实况为微量降水的特殊情况
            else if ("微量降水".equals(actualLevel)) {
                // 微量降水特殊规则：预报为小雨、无雨或微量降水都算正确
                if ("小雨".equals(level)) {
                    // 如果当前评分的是小雨量级，且实况为微量降水
                    if ("小雨".equals(forecastLevel) || "无雨".equals(forecastLevel) || "微量降水".equals(forecastLevel)) {
                        A++; // 算作小雨的正确预报（包括预报微量降水的情况）
                    } else {
                        B++; // 预报为其他量级（中雨及以上）
                    }
                }
            }
        }

        int total = A + B + C;
        return total > 0 ? (double)A / total : 0.0;
    }



    /**
     * 计算基础分
     */
    private Map<String, Double> calculateBaseScores(Map<String, Double> studentTS,
                                                   Map<String, Double> cmaMesoTS) {
        Map<String, Double> baseScores = new HashMap<>();

        for (String level : studentTS.keySet()) {
            double studentScore = studentTS.get(level);
            double cmaScore = cmaMesoTS.getOrDefault(level, 0.0);
            double baseScore = studentScore >= cmaScore ? 0.3 : 0.0;

            log.debug("{}基础分计算：学生TS={}, MESO TS={}, 基础分={}",
                level, studentScore, cmaScore, baseScore);

            baseScores.put(level, baseScore);
        }

        return baseScores;
    }

    /**
     * 计算技巧评分
     */
    private Map<String, Double> calculateSkillScores(Map<String, Double> studentTS,
                                                     Map<String, Double> baseScores) {
        Map<String, Double> skillScores = new HashMap<>();

        for (String level : studentTS.keySet()) {
            double baseScore = baseScores.get(level);
            double tsScore = studentTS.get(level);
            double skillScore = baseScore + tsScore * 0.7;

            log.debug("{}技巧评分计算：基础分={}, TS评分={}, 技巧评分={}",
                level, baseScore, tsScore, skillScore);

            skillScores.put(level, skillScore);
        }

        return skillScores;
    }

    /**
     * 计算加权最终评分
     */
    private double calculateWeightedFinalScore(Map<String, Double> skillScores) {
        Map<String, Double> weights = new HashMap<>();
        weights.put("晴雨", 0.1);
        weights.put("小雨", 0.2);
        weights.put("中雨", 0.2);
        weights.put("大雨", 0.2);
        weights.put("暴雨", 0.2);
        weights.put("大暴雨", 0.1); // 恢复大暴雨权重，总权重为1.0

        double weightedSum = 0.0;
        for (String level : skillScores.keySet()) {
            double skillScore = skillScores.get(level);
            double weight = weights.getOrDefault(level, 0.0);
            weightedSum += skillScore * weight;
        }

        return weightedSum * 40.0; // 乘以总分40分
    }

    /**
     * 根据区域编码筛选站点数据
     *
     * @param actualData 原始站点数据
     * @param regionCode 区域编码（1-9）
     * @return 筛选后的站点数据，包含区域内所有站点，缺失数据的站点降水量设为0
     */
    private List<StationPrecipitationData> filterStationsByRegion(List<StationPrecipitationData> actualData, String regionCode) {
        try {
            // 创建区域查询请求DTO
            ElStationRegionReqDTO reqDTO = new ElStationRegionReqDTO();
            reqDTO.setRegionCode(regionCode);

            // 设置站点等级列表（国家站、基准站、基本站）
            reqDTO.setStationLevls(Arrays.asList("11", "12", "13"));

            // 获取该区域的所有站点
            List<ElStationRespDTO> regionStations = stationService.listByRegionCodes(reqDTO);
            if (regionStations.isEmpty()) {
                log.warn("区域编码{}没有找到对应的站点", regionCode);
                return actualData; // 如果没有找到区域站点，返回原始数据
            }

            // 创建实况数据的站点ID映射，用于快速查找
            Map<Long, StationPrecipitationData> actualDataMap = actualData.stream()
                .collect(Collectors.toMap(
                    StationPrecipitationData::getStationId,
                    data -> data,
                    (existing, replacement) -> existing // 如果有重复站点ID，保留第一个
                ));

            // 构建结果列表，确保包含区域内所有站点
            List<StationPrecipitationData> resultData = new ArrayList<>();
            int existingDataCount = 0;
            int missingDataCount = 0;

            for (ElStationRespDTO regionStation : regionStations) {
                String stationIdStr = regionStation.getStationIdC();
                if (stationIdStr == null) {
                    continue;
                }

                try {
                    long stationId = Long.parseLong(stationIdStr);

                    // 检查实况数据中是否存在该站点
                    StationPrecipitationData existingData = actualDataMap.get(stationId);
                    if (existingData != null) {
                        // 使用现有的实况数据
                        resultData.add(existingData);
                        existingDataCount++;
                    } else {
                        // 创建缺失站点的数据，降水量设为0
                        StationPrecipitationData missingData = new StationPrecipitationData();
                        missingData.setStationId(stationId);
                        missingData.setLongitude(regionStation.getLon() != null ? regionStation.getLon() : 0.0);
                        missingData.setLatitude(regionStation.getLat() != null ? regionStation.getLat() : 0.0);
                        missingData.setActualPrecipitation(0.0);
                        missingData.setActualLevel(classifyPrecipitationLevel(0.0)); // 应该返回"无雨"

                        resultData.add(missingData);
                        missingDataCount++;

                        log.debug("为缺失站点{}创建无降水数据，位置：({}, {})",
                            stationId, missingData.getLongitude(), missingData.getLatitude());
                    }
                } catch (NumberFormatException e) {
                    log.warn("站点ID格式错误，跳过站点：{}", stationIdStr);
                }
            }

            log.info("区域{}包含{}个站点，实况数据{}个站点，其中{}个有实况数据，{}个缺失数据（已补充为无降水）",
                regionCode, regionStations.size(), actualData.size(), existingDataCount, missingDataCount);

            return resultData;

        } catch (Exception e) {
            log.error("根据区域编码筛选站点数据失败，区域编码：{}", regionCode, e);
            return actualData; // 出错时返回原始数据
        }
    }

    /**
     * 计算各量级TS评分并收集详细信息
     */
    private Map<String, Double> calculateAllLevelTSWithDetails(List<StationPrecipitationData> forecastData,
                                                              List<StationPrecipitationData> actualData,
                                                              List<LevelTSScoringDetail> levelTSDetails,
                                                              boolean collectDetails,
                                                              String evaluatorType) {
        Map<String, Double> tsScores = new HashMap<>();

        // 晴雨TS评分
        LevelTSScoringDetail rainNoRainDetail = null;
        if (collectDetails) {
            // 查找是否已存在晴雨评分详情
            rainNoRainDetail = levelTSDetails.stream()
                .filter(detail -> "晴雨".equals(detail.getLevel()))
                .findFirst()
                .orElse(null);

            if (rainNoRainDetail == null) {
                rainNoRainDetail = new LevelTSScoringDetail();
                rainNoRainDetail.setLevel("晴雨");
                rainNoRainDetail.setStudentTSStats(new LevelTSScoringDetail.TSStatistics());
                rainNoRainDetail.setCmaMesoTSStats(new LevelTSScoringDetail.TSStatistics());
                rainNoRainDetail.setTotalStations(actualData.size());
                levelTSDetails.add(rainNoRainDetail);
            }
        }

        double rainNoRainTS = calculateRainNoRainTSWithDetails(forecastData, actualData, rainNoRainDetail, evaluatorType);
        tsScores.put("晴雨", rainNoRainTS);

        // 各量级TS评分（微量降水不单独评分）
        String[] levels = {"小雨", "中雨", "大雨", "暴雨", "大暴雨"};
        for (String level : levels) {
            LevelTSScoringDetail levelDetail = null;
            if (collectDetails) {
                // 查找是否已存在该量级的评分详情
                levelDetail = levelTSDetails.stream()
                    .filter(detail -> level.equals(detail.getLevel()))
                    .findFirst()
                    .orElse(null);

                if (levelDetail == null) {
                    levelDetail = new LevelTSScoringDetail();
                    levelDetail.setLevel(level);
                    levelDetail.setStudentTSStats(new LevelTSScoringDetail.TSStatistics());
                    levelDetail.setCmaMesoTSStats(new LevelTSScoringDetail.TSStatistics());

                    // 统计参与该量级评分的站点数（包括微量降水对小雨的贡献）
                    int participatingStations;
                    if ("小雨".equals(level)) {
                        // 小雨量级包括实况为小雨和微量降水的站点
                        participatingStations = (int) actualData.stream()
                            .mapToLong(data -> ("小雨".equals(data.getActualLevel()) || "微量降水".equals(data.getActualLevel())) ? 1 : 0)
                            .sum();
                        levelDetail.setSpecialRuleNote("包含微量降水特殊规则：实况为微量降水时，预报小雨或无雨都算正确");
                    } else {
                        participatingStations = (int) actualData.stream()
                            .mapToLong(data -> level.equals(data.getActualLevel()) ? 1 : 0)
                            .sum();
                    }
                    levelDetail.setTotalStations(participatingStations);

                    levelTSDetails.add(levelDetail);
                }
            }

            double levelTS = calculateLevelTSWithDetails(forecastData, actualData, level, levelDetail, evaluatorType);
            tsScores.put(level, levelTS);
        }

        return tsScores;
    }

    /**
     * 计算晴雨TS评分并收集详细信息
     */
    private double calculateRainNoRainTSWithDetails(List<StationPrecipitationData> forecastData,
                                                   List<StationPrecipitationData> actualData,
                                                   LevelTSScoringDetail rainNoRainDetail,
                                                   String evaluatorType) {
        int A = 0, B = 0, C = 0, D = 0;

        for (int i = 0; i < actualData.size(); i++) {
            StationPrecipitationData actual = actualData.get(i);
            StationPrecipitationData forecast = forecastData.get(i);

            String actualLevel = actual.getActualLevel();
            String forecastLevel = forecast.getForecastLevel();

            boolean actualRain = actual.hasActualRain();
            boolean forecastRain = forecast.hasForecastRain();

            // 微量降水特殊处理：实况为微量降水时，预报无雨或小雨都算正确
            if ("微量降水".equals(actualLevel)) {
                if ("无雨".equals(forecastLevel) || "小雨".equals(forecastLevel) || "微量降水".equals(forecastLevel)) {
                    A++; // 正确预报有降水（微量降水特殊规则）
                    if (rainNoRainDetail != null) {
                        LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                            rainNoRainDetail.getStudentTSStats() : rainNoRainDetail.getCmaMesoTSStats();
                        stats.addCorrect();
                    }
                } else {
                    B++; // 空报（预报为中雨及以上）
                    if (rainNoRainDetail != null) {
                        LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                            rainNoRainDetail.getStudentTSStats() : rainNoRainDetail.getCmaMesoTSStats();
                        stats.addWrong();
                    }
                }
            } else {
                // 常规晴雨评分逻辑
                if (forecastRain && actualRain) {
                    A++; // 正确预报有降水
                    if (rainNoRainDetail != null) {
                        LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                            rainNoRainDetail.getStudentTSStats() : rainNoRainDetail.getCmaMesoTSStats();
                        stats.addCorrect();
                    }
                } else if (forecastRain && !actualRain) {
                    B++; // 空报
                    if (rainNoRainDetail != null) {
                        LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                            rainNoRainDetail.getStudentTSStats() : rainNoRainDetail.getCmaMesoTSStats();
                        stats.addWrong();
                    }
                } else if (!forecastRain && actualRain) {
                    C++; // 漏报
                    if (rainNoRainDetail != null) {
                        LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                            rainNoRainDetail.getStudentTSStats() : rainNoRainDetail.getCmaMesoTSStats();
                        stats.addMissed();
                    }
                } else {
                    D++; // 正确预报无降水
                    if (rainNoRainDetail != null) {
                        LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                            rainNoRainDetail.getStudentTSStats() : rainNoRainDetail.getCmaMesoTSStats();
                        stats.addCorrect();
                    }
                }
            }
        }

        int total = A + B + C + D;
        // 晴雨TS评分公式：TS = (A + D) / (A + B + C + D)
        // A: 正确预报有降水, B: 空报, C: 漏报, D: 正确预报无降水
        double tsScore = total > 0 ? (double)(A + D) / total : 0.0;

        if (rainNoRainDetail != null) {
            LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                rainNoRainDetail.getStudentTSStats() : rainNoRainDetail.getCmaMesoTSStats();
            stats.calculateTS();
        }

        return tsScore;
    }

    /**
     * 计算指定量级TS评分并收集详细信息
     */
    private double calculateLevelTSWithDetails(List<StationPrecipitationData> forecastData,
                                              List<StationPrecipitationData> actualData,
                                              String level,
                                              LevelTSScoringDetail levelDetail,
                                              String evaluatorType) {
        int A = 0, B = 0, C = 0;

        for (int i = 0; i < actualData.size(); i++) {
            StationPrecipitationData actual = actualData.get(i);
            StationPrecipitationData forecast = forecastData.get(i);

            String actualLevel = actual.getActualLevel();
            String forecastLevel = forecast.getForecastLevel();

            // 处理实况为该量级的站点
            if (level.equals(actualLevel)) {
                if (level.equals(forecastLevel)) {
                    A++; // 正确预报该量级
                    if (levelDetail != null) {
                        LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                            levelDetail.getStudentTSStats() : levelDetail.getCmaMesoTSStats();
                        stats.addCorrect();
                    }
                } else if (!"无雨".equals(forecastLevel)) {
                    B++; // 预报为其他量级
                    if (levelDetail != null) {
                        LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                            levelDetail.getStudentTSStats() : levelDetail.getCmaMesoTSStats();
                        stats.addWrong();
                    }
                } else {
                    C++; // 漏报（预报无雨）
                    if (levelDetail != null) {
                        LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                            levelDetail.getStudentTSStats() : levelDetail.getCmaMesoTSStats();
                        stats.addMissed();
                    }
                }
            }
            // 处理实况为微量降水的特殊情况
            else if ("微量降水".equals(actualLevel)) {
                // 微量降水特殊规则：在小雨量级评分中，预报为微量降水、小雨或无雨都算正确
                if ("小雨".equals(level)) {
                    // 如果当前评分的是小雨量级，且实况为微量降水
                    if ("微量降水".equals(forecastLevel) || "小雨".equals(forecastLevel) || "无雨".equals(forecastLevel)) {
                        A++; // 算作小雨的正确预报
                        if (levelDetail != null) {
                            LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                                levelDetail.getStudentTSStats() : levelDetail.getCmaMesoTSStats();
                            stats.addCorrect();
                        }
                    } else {
                        B++; // 预报为其他量级（中雨及以上）
                        if (levelDetail != null) {
                            LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                                levelDetail.getStudentTSStats() : levelDetail.getCmaMesoTSStats();
                            stats.addWrong();
                        }
                    }
                }
            }
        }

        int total = A + B + C;
        double tsScore = total > 0 ? (double)A / total : 0.0;

        if (levelDetail != null) {
            LevelTSScoringDetail.TSStatistics stats = "student".equals(evaluatorType) ?
                levelDetail.getStudentTSStats() : levelDetail.getCmaMesoTSStats();
            stats.calculateTS();
        }

        return tsScore;
    }



    /**
     * 生成站点详细评分信息
     */
    private List<StationScoringDetail> generateStationDetails(List<StationPrecipitationData> actualData,
                                                              List<StationPrecipitationData> studentData,
                                                              List<StationPrecipitationData> cmaMesoData) {
        List<StationScoringDetail> stationDetails = new ArrayList<>();

        for (int i = 0; i < actualData.size(); i++) {
            StationPrecipitationData actual = actualData.get(i);
            StationPrecipitationData student = studentData.get(i);
            StationPrecipitationData cmaMeso = cmaMesoData.get(i);

            StationScoringDetail detail = new StationScoringDetail();
            detail.setStationId(actual.getStationId());
            detail.setLongitude(actual.getLongitude());
            detail.setLatitude(actual.getLatitude());
            detail.setActualPrecipitation(actual.getActualPrecipitation());
            detail.setActualLevel(actual.getActualLevel());
            detail.setStudentForecastPrecipitation(student.getForecastPrecipitation());
            detail.setStudentForecastLevel(student.getForecastLevel());
            detail.setCmaMesoForecastPrecipitation(cmaMeso.getForecastPrecipitation());
            detail.setCmaMesoForecastLevel(cmaMeso.getForecastLevel());

            // 生成晴雨评分详情
            StationScoringDetail.RainNoRainScoringDetail rainNoRainDetail =
                new StationScoringDetail.RainNoRainScoringDetail();
            rainNoRainDetail.setActualHasRain(actual.hasActualRain());
            rainNoRainDetail.setStudentForecastHasRain(student.hasForecastRain());
            rainNoRainDetail.setCmaMesoForecastHasRain(cmaMeso.hasForecastRain());

            // 判断晴雨预报结果类型
            rainNoRainDetail.setStudentResultType(getRainNoRainResultType(actual, student));
            rainNoRainDetail.setCmaMesoResultType(getRainNoRainResultType(actual, cmaMeso));
            rainNoRainDetail.setStudentContribution(getRainNoRainContribution(actual, student));
            rainNoRainDetail.setCmaMesoContribution(getRainNoRainContribution(actual, cmaMeso));

            detail.setRainNoRainDetail(rainNoRainDetail);

            // 生成量级评分详情（以实况量级为准）
            if (!"无雨".equals(actual.getActualLevel())) {
                StationScoringDetail.LevelScoringDetail levelDetail =
                    new StationScoringDetail.LevelScoringDetail();
                levelDetail.setLevel(actual.getActualLevel());
                levelDetail.setParticipateInScoring(true);

                // 判断量级预报结果类型
                levelDetail.setStudentResultType(getLevelResultType(actual, student));
                levelDetail.setCmaMesoResultType(getLevelResultType(actual, cmaMeso));
                levelDetail.setStudentContribution(getLevelContribution(actual, student));
                levelDetail.setCmaMesoContribution(getLevelContribution(actual, cmaMeso));

                // 微量降水特殊规则说明
                if ("微量降水".equals(actual.getActualLevel())) {
                    levelDetail.setSpecialRuleNote("微量降水特殊规则：预报为微量降水、小雨或无雨都算正确");
                }

                detail.setLevelDetail(levelDetail);
            }

            stationDetails.add(detail);
        }

        return stationDetails;
    }

    /**
     * 获取晴雨预报结果类型
     */
    private String getRainNoRainResultType(StationPrecipitationData actual, StationPrecipitationData forecast) {
        String actualLevel = actual.getActualLevel();
        String forecastLevel = forecast.getForecastLevel();

        // 微量降水特殊处理：实况为微量降水时，预报无雨、小雨或微量降水都算正确
        if ("微量降水".equals(actualLevel)) {
            if ("无雨".equals(forecastLevel) || "小雨".equals(forecastLevel) || "微量降水".equals(forecastLevel)) {
                return "正确A";
            } else {
                return "空报B"; // 预报为中雨及以上
            }
        }

        // 常规晴雨评分逻辑
        boolean actualRain = actual.hasActualRain();
        boolean forecastRain = forecast.hasForecastRain();

        if (forecastRain && actualRain) {
            return "正确A";
        } else if (forecastRain && !actualRain) {
            return "空报B";
        } else if (!forecastRain && actualRain) {
            return "漏报C";
        } else {
            return "正确D";
        }
    }

    /**
     * 获取晴雨预报对TS的贡献
     */
    private String getRainNoRainContribution(StationPrecipitationData actual, StationPrecipitationData forecast) {
        String resultType = getRainNoRainResultType(actual, forecast);
        if ("正确A".equals(resultType) || "正确D".equals(resultType)) {
            return "正确+1";
        } else {
            return "错误+1";
        }
    }

    /**
     * 获取量级预报结果类型
     */
    private String getLevelResultType(StationPrecipitationData actual, StationPrecipitationData forecast) {
        String actualLevel = actual.getActualLevel();
        String forecastLevel = forecast.getForecastLevel();

        // 微量降水特殊处理
        if ("微量降水".equals(actualLevel)) {
            if ("微量降水".equals(forecastLevel) || "小雨".equals(forecastLevel) || "无雨".equals(forecastLevel)) {
                return "正确A";
            } else {
                return "错误B";
            }
        }

        // 其他量级常规处理
        if (actualLevel.equals(forecastLevel)) {
            return "正确A";
        } else if ("无雨".equals(forecastLevel)) {
            return "漏报C";
        } else {
            return "错误B";
        }
    }

    /**
     * 获取量级预报对TS的贡献
     */
    private String getLevelContribution(StationPrecipitationData actual, StationPrecipitationData forecast) {
        String resultType = getLevelResultType(actual, forecast);
        if ("正确A".equals(resultType)) {
            return "正确+1";
        } else if ("错误B".equals(resultType)) {
            return "错误+1";
        } else {
            return "漏报+1";
        }
    }

    /**
     * 更新量级详情中的评分信息
     */
    private void updateLevelDetailsWithScores(List<LevelTSScoringDetail> levelTSDetails,
                                             Map<String, Double> baseScores,
                                             Map<String, Double> skillScores) {
        Map<String, Double> weights = getWeightsMap();

        for (LevelTSScoringDetail detail : levelTSDetails) {
            String level = detail.getLevel();
            detail.setStudentBaseScore(baseScores.getOrDefault(level, 0.0));
            detail.setStudentSkillScore(skillScores.getOrDefault(level, 0.0));
            detail.setWeight(weights.getOrDefault(level, 0.0));
            detail.calculateContribution();
        }
    }

    /**
     * 获取权重配置
     */
    private Map<String, Double> getWeightsMap() {
        Map<String, Double> weights = new HashMap<>();
        weights.put("晴雨", 0.1);
        weights.put("小雨", 0.2);
        weights.put("中雨", 0.2);
        weights.put("大雨", 0.2);
        weights.put("暴雨", 0.2);
        weights.put("大暴雨", 0.1);
        return weights;
    }

    /**
     * 生成meso晴雨TS评分详细记录
     */
    private Map<String, Object> generateMesoRainNoRainDetails(List<StationPrecipitationData> cmaMesoData,
                                                             List<StationPrecipitationData> actualData) {
        Map<String, Object> details = new HashMap<>();

        int correctA = 0, correctD = 0, wrongB = 0, missedC = 0;
        List<Map<String, Object>> stationRecords = new ArrayList<>();

        for (int i = 0; i < actualData.size(); i++) {
            StationPrecipitationData actual = actualData.get(i);
            StationPrecipitationData meso = cmaMesoData.get(i);

            boolean actualRain = actual.hasActualRain();
            boolean mesoRain = meso.hasForecastRain();

            Map<String, Object> stationRecord = new HashMap<>();
            stationRecord.put("stationId", actual.getStationId());
            stationRecord.put("longitude", actual.getLongitude());
            stationRecord.put("latitude", actual.getLatitude());
            stationRecord.put("actualLevel", actual.getActualLevel());
            stationRecord.put("mesoLevel", meso.getForecastLevel());
            stationRecord.put("actualRain", actualRain);
            stationRecord.put("mesoRain", mesoRain);

            // 微量降水特殊处理
            if ("微量降水".equals(actual.getActualLevel())) {
                if ("无雨".equals(meso.getForecastLevel()) || "小雨".equals(meso.getForecastLevel()) || "微量降水".equals(meso.getForecastLevel())) {
                    correctA++;
                    stationRecord.put("result", "正确A");
                    stationRecord.put("note", "微量降水特殊规则：预报无雨/小雨/微量降水都算正确");
                } else {
                    wrongB++;
                    stationRecord.put("result", "错误B");
                    stationRecord.put("note", "微量降水但预报为中雨及以上");
                }
            } else {
                // 常规晴雨评分逻辑
                if (mesoRain && actualRain) {
                    correctA++;
                    stationRecord.put("result", "正确A");
                    stationRecord.put("note", "正确预报有降水");
                } else if (mesoRain && !actualRain) {
                    wrongB++;
                    stationRecord.put("result", "错误B");
                    stationRecord.put("note", "空报降水");
                } else if (!mesoRain && actualRain) {
                    missedC++;
                    stationRecord.put("result", "漏报C");
                    stationRecord.put("note", "漏报降水");
                } else {
                    correctD++;
                    stationRecord.put("result", "正确D");
                    stationRecord.put("note", "正确预报无降水");
                }
            }

            stationRecords.add(stationRecord);
        }

        int total = correctA + correctD + wrongB + missedC;
        // 标准TS评分公式：TS = correctA / (correctA + wrongB + missedC)
        // 对于晴雨TS评分，只考虑正确预报有雨的情况
        double tsScore = (correctA + wrongB + missedC) > 0 ? (double)correctA / (correctA + wrongB + missedC) : 0.0;

        details.put("correctA", correctA);
        details.put("correctD", correctD);
        details.put("wrongB", wrongB);
        details.put("missedC", missedC);
        details.put("total", total);
        details.put("tsScore", tsScore);
        details.put("formula", String.format("TS = %d / (%d + %d + %d) = %.3f",
            correctA, correctA, wrongB, missedC, tsScore));
        details.put("stationRecords", stationRecords);
        details.put("summary", String.format("meso晴雨TS评分：正确预报有雨%d个，正确预报无雨%d个，空报%d个，漏报%d个，TS评分%.3f",
            correctA, correctD, wrongB, missedC, tsScore));

        return details;
    }

    /**
     * 生成meso各量级TS评分详细记录
     */
    private Map<String, Object> generateMesoLevelTSDetails(List<StationPrecipitationData> cmaMesoData,
                                                          List<StationPrecipitationData> actualData,
                                                          Map<String, Double> cmaMesoTSScores) {
        Map<String, Object> details = new HashMap<>();
        String[] levels = {"小雨", "中雨", "大雨", "暴雨", "大暴雨"};

        for (String level : levels) {
            Map<String, Object> levelDetail = new HashMap<>();

            int correctA = 0, wrongB = 0, missedC = 0;
            List<Map<String, Object>> stationRecords = new ArrayList<>();

            for (int i = 0; i < actualData.size(); i++) {
                StationPrecipitationData actual = actualData.get(i);
                StationPrecipitationData meso = cmaMesoData.get(i);

                String actualLevel = actual.getActualLevel();
                String mesoLevel = meso.getForecastLevel();

                // 只处理实况为该量级或微量降水（对小雨量级）的站点
                boolean shouldProcess = level.equals(actualLevel) ||
                    ("小雨".equals(level) && "微量降水".equals(actualLevel));

                if (shouldProcess) {
                    Map<String, Object> stationRecord = new HashMap<>();
                    stationRecord.put("stationId", actual.getStationId());
                    stationRecord.put("longitude", actual.getLongitude());
                    stationRecord.put("latitude", actual.getLatitude());
                    stationRecord.put("actualLevel", actualLevel);
                    stationRecord.put("mesoLevel", mesoLevel);

                    // 处理实况为该量级的站点
                    if (level.equals(actualLevel)) {
                        if (level.equals(mesoLevel)) {
                            correctA++;
                            stationRecord.put("result", "正确A");
                            stationRecord.put("note", "正确预报该量级");
                        } else if (!"无雨".equals(mesoLevel)) {
                            wrongB++;
                            stationRecord.put("result", "错误B");
                            stationRecord.put("note", "预报为其他量级");
                        } else {
                            missedC++;
                            stationRecord.put("result", "漏报C");
                            stationRecord.put("note", "漏报（预报无雨）");
                        }
                    }
                    // 处理实况为微量降水的特殊情况（仅对小雨量级）
                    else if ("微量降水".equals(actualLevel) && "小雨".equals(level)) {
                        if ("微量降水".equals(mesoLevel) || "小雨".equals(mesoLevel) || "无雨".equals(mesoLevel)) {
                            correctA++;
                            stationRecord.put("result", "正确A");
                            stationRecord.put("note", "微量降水特殊规则：预报微量降水/小雨/无雨都算正确");
                        } else {
                            wrongB++;
                            stationRecord.put("result", "错误B");
                            stationRecord.put("note", "微量降水但预报为中雨及以上");
                        }
                    }

                    stationRecords.add(stationRecord);
                }
            }

            int total = correctA + wrongB + missedC;
            double tsScore = total > 0 ? (double)correctA / total : 0.0;

            levelDetail.put("level", level);
            levelDetail.put("correctA", correctA);
            levelDetail.put("wrongB", wrongB);
            levelDetail.put("missedC", missedC);
            levelDetail.put("total", total);
            levelDetail.put("tsScore", tsScore);
            levelDetail.put("formula", String.format("TS = %d / (%d + %d + %d) = %.3f",
                correctA, correctA, wrongB, missedC, tsScore));
            levelDetail.put("stationRecords", stationRecords);
            levelDetail.put("summary", String.format("meso %s TS评分：正确%d个，错误%d个，漏报%d个，TS评分%.3f",
                level, correctA, wrongB, missedC, tsScore));

            details.put(level, levelDetail);
        }

        return details;
    }

    /**
     * 生成考生晴雨TS评分详细记录
     */
    private Map<String, Object> generateStudentRainNoRainDetails(List<StationPrecipitationData> studentData,
                                                                 List<StationPrecipitationData> actualData) {
        Map<String, Object> details = new HashMap<>();

        int correctA = 0, correctD = 0, wrongB = 0, missedC = 0;
        List<Map<String, Object>> stationRecords = new ArrayList<>();

        for (int i = 0; i < actualData.size(); i++) {
            StationPrecipitationData actual = actualData.get(i);
            StationPrecipitationData student = studentData.get(i);

            boolean actualRain = actual.hasActualRain();
            boolean studentRain = student.hasForecastRain();

            Map<String, Object> record = new HashMap<>();
            record.put("stationId", actual.getStationId());
            record.put("actualRain", actualRain);
            record.put("studentRain", studentRain);

            if (studentRain && actualRain) {
                correctA++;
                record.put("result", "正确预报有雨");
                record.put("type", "A");
            } else if (studentRain && !actualRain) {
                wrongB++;
                record.put("result", "空报");
                record.put("type", "B");
            } else if (!studentRain && actualRain) {
                missedC++;
                record.put("result", "漏报");
                record.put("type", "C");
            } else {
                correctD++;
                record.put("result", "正确预报无雨");
                record.put("type", "D");
            }

            stationRecords.add(record);
        }

        int total = correctA + correctD + wrongB + missedC;
        // 标准TS评分公式：TS = correctA / (correctA + wrongB + missedC)
        // 对于晴雨TS评分，只考虑正确预报有雨的情况
        double tsScore = (correctA + wrongB + missedC) > 0 ? (double)correctA / (correctA + wrongB + missedC) : 0.0;

        details.put("correctA", correctA);
        details.put("correctD", correctD);
        details.put("wrongB", wrongB);
        details.put("missedC", missedC);
        details.put("total", total);
        details.put("tsScore", tsScore);
        details.put("formula", String.format("TS = %d / (%d + %d + %d) = %.3f",
            correctA, correctA, wrongB, missedC, tsScore));
        details.put("stationRecords", stationRecords);
        details.put("summary", String.format("考生晴雨TS评分：正确预报有雨%d个，正确预报无雨%d个，空报%d个，漏报%d个，TS评分%.3f",
            correctA, correctD, wrongB, missedC, tsScore));

        return details;
    }

    /**
     * 生成考生各量级TS评分详细记录（包含晴雨TS评分）
     */
    private Map<String, Object> generateStudentLevelTSDetails(List<StationPrecipitationData> studentData,
                                                             List<StationPrecipitationData> actualData,
                                                             Map<String, Double> studentTSScores) {
        Map<String, Object> details = new HashMap<>();

        // 首先添加晴雨TS评分
        Map<String, Object> rainNoRainDetail = generateStudentRainNoRainDetails(studentData, actualData);
        details.put("晴雨", rainNoRainDetail);

        // 然后添加各量级TS评分
        String[] levels = {"小雨", "中雨", "大雨", "暴雨", "大暴雨"};

        for (String level : levels) {
            Map<String, Object> levelDetail = new HashMap<>();

            int correctA = 0, wrongB = 0, missedC = 0;
            List<Map<String, Object>> stationRecords = new ArrayList<>();

            for (int i = 0; i < actualData.size(); i++) {
                StationPrecipitationData actual = actualData.get(i);
                StationPrecipitationData student = studentData.get(i);

                // 判断实况是否为该量级（包括微量降水特殊规则）
                boolean actualIsLevel = level.equals(actual.getActualLevel()) ||
                    ("小雨".equals(level) && "微量降水".equals(actual.getActualLevel()));

                if (!actualIsLevel) {
                    continue; // 只统计实况为该量级的站点
                }

                String studentLevel = student.getForecastLevel();
                boolean studentIsLevel = level.equals(studentLevel) ||
                    ("小雨".equals(level) && "微量降水".equals(studentLevel));

                Map<String, Object> record = new HashMap<>();
                record.put("stationId", actual.getStationId());
                record.put("actualLevel", actual.getActualLevel());
                record.put("studentLevel", studentLevel);

                if (studentIsLevel) {
                    correctA++;
                    record.put("result", "正确预报");
                    record.put("type", "A");
                } else if ("无雨".equals(studentLevel)) {
                    missedC++;
                    record.put("result", "漏报");
                    record.put("type", "C");
                } else {
                    wrongB++;
                    record.put("result", "错误预报");
                    record.put("type", "B");
                }

                stationRecords.add(record);
            }

            int total = correctA + wrongB + missedC;
            double tsScore = total > 0 ? (double)correctA / total : 0.0;

            levelDetail.put("level", level);
            levelDetail.put("correctA", correctA);
            levelDetail.put("wrongB", wrongB);
            levelDetail.put("missedC", missedC);
            levelDetail.put("total", total);
            levelDetail.put("tsScore", tsScore);
            levelDetail.put("formula", String.format("TS = %d / (%d + %d + %d) = %.3f",
                correctA, correctA, wrongB, missedC, tsScore));
            levelDetail.put("stationRecords", stationRecords);
            levelDetail.put("summary", String.format("考生 %s TS评分：正确%d个，错误%d个，漏报%d个，TS评分%.3f",
                level, correctA, wrongB, missedC, tsScore));

            details.put(level, levelDetail);
        }

        return details;
    }

    /**
     * 将MESO晴雨TS评分详情集成到各量级评分中
     * MESO晴雨TS评分详情将放在各量级评分的上方，基础评分和技能评分放在MESO评分的下方
     */
    private void integrateMesoDetailsIntoLevelScoring(List<LevelTSScoringDetail> levelTSDetails,
                                                     Map<String, Object> mesoRainNoRainDetails,
                                                     Map<String, Object> mesoLevelTSDetails) {

        // 为每个量级添加MESO相关的详细信息
        for (LevelTSScoringDetail detail : levelTSDetails) {
            String level = detail.getLevel();

            // 为晴雨量级添加MESO晴雨TS评分详情
            if ("晴雨".equals(level)) {
                // 直接存储MESO晴雨TS评分详情
                detail.setMesoRainNoRainDetails(mesoRainNoRainDetails);

                // 更新特殊说明
                StringBuilder noteBuilder = new StringBuilder();
                if (detail.getSpecialRuleNote() != null) {
                    noteBuilder.append(detail.getSpecialRuleNote()).append("；");
                }
                noteBuilder.append("MESO晴雨TS评分详情已集成，基础评分和技能评分位于MESO评分下方");
                detail.setSpecialRuleNote(noteBuilder.toString());
            }
            // 为其他量级添加对应的MESO量级TS评分详情
            else if (mesoLevelTSDetails != null && mesoLevelTSDetails.containsKey(level)) {
                Map<String, Object> mesoLevelDetail = (Map<String, Object>) mesoLevelTSDetails.get(level);
                if (mesoLevelDetail != null) {
                    // 直接存储MESO该量级TS评分详情
                    detail.setMesoLevelDetails(mesoLevelDetail);

                    // 更新特殊说明
                    StringBuilder noteBuilder = new StringBuilder();
                    if (detail.getSpecialRuleNote() != null) {
                        noteBuilder.append(detail.getSpecialRuleNote()).append("；");
                    }
                    noteBuilder.append("MESO").append(level).append("TS评分详情已集成，基础评分和技能评分位于MESO评分下方");
                    detail.setSpecialRuleNote(noteBuilder.toString());
                }
            }
        }
    }
}
